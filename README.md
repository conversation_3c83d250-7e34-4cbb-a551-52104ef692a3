# Dividend Tracker API

A Node.js backend for Dividend Tracker APP, built with Express.js and TypeScript.

## Getting Started

### Prerequisites

- Node.js (v22)
- Yarn
- MySQL database
- Redis server

### Installation

```sh
yarn install
```

3. Copy the environment example file and configure it

```sh
cp .env.example .env
```

4. Run database migrations (mysql docker service should be running)

```sh
yarn migrate
```

## Development

Start the development server:

```sh
# start mysql and redis services via docker
docker compose up -d --build

# start dev server
yarn dev
```

Run tests:

```sh
yarn test
```

## Production

Build the application:

```sh
yarn build
```

Start the production server:

```sh
yarn start
```

Or:

```sh
NODE_ENV=production npx nx run api:serve:production
```

## API Endpoints

### Authentication

- `POST /api/v1/auth/login` - User login
- `POST /api/v1/auth/register` - User registration
- `GET /api/v1/auth/session` - Validate and refresh session
- `DELETE /api/v1/auth/logout` - User logout

### Portfolio Goals

- `GET /api/v1/goals` - Get all user goals
- `POST /api/v1/goals` - Create new goal(s)
- `PATCH /api/v1/goals/:id` - Update a specific goal
- `DELETE /api/v1/goals/:id` - Delete a specific goal

### Holdings

- `GET /api/v1/holdings` - Get all user holdings
- `POST /api/v1/holdings` - Create new holding(s)
- `PATCH /api/v1/holdings` - Batch update holdings
- `PATCH /api/v1/holdings/:id` - Update a specific holding
- `DELETE /api/v1/holdings/:id` - Delete a specific holding

### Reports

- `GET /api/v1/reports` - Get all user reports
- `POST /api/v1/reports` - Create new report(s)
- `PATCH /api/v1/reports/:id` - Update a specific report
- `DELETE /api/v1/reports/:id` - Delete a specific report

### Currency Rates

- `GET /api/v1/rates` - Get all currency rates

### Tasks (Admin only)

- `GET /api/v1/tasks/currencies` - Get currency rates from external source
- `POST /api/v1/tasks/currencies/update` - Update currency rates

### User Management

- `PATCH /api/v1/users/:id` - Update user information

## Architecture

This application follows a model-controller architecture:

- **Models**: Data structures and database operations
- **Controllers**: Request handling and business logic
- **Services**: Shared functionality and external integrations
- **Adapters**: Database and external API interactions

All API routes are configured to use environment variables for consistent namespace management across environments. Authentication middleware secures all endpoints except for public auth routes.

## Scheduled Tasks

The application runs a daily job at 3:00 AM Pacific Time to update currency exchange rates automatically.

## Testing

Run tests + coverage with:

```sh
yarn test
```
