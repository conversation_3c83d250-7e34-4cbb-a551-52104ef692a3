import { currencies } from '@api/lib/constants/currencies';
import { fetchCurrenciesTask } from '@api/services/tasks';

const tableName = 'currency_rates';

export const up = (db, done) => {
  fetchCurrenciesTask()
    .then((currencyData) => {
      const queryValues = [];

      for (let i = 0, len = currencyData.length; i < len; i++) {
        const item = currencyData[i];
        const currency = currencies.find((c) => c.code === item.code);

        if (currency) {
          queryValues.push(`(${currency.id}, "${currency.code}", ${Math.floor(Date.now() / 1000)}, ${item.rate})`);
        }
      }

      const sql = `TRUNCATE ${tableName}; INSERT INTO ${tableName} (id, code, dtUpdated, rate) VALUES ${queryValues.join(',')};`;

      db.runSql(sql, (err) => {
        if (err) {
          throw new Error(err);
        }

        done();
      });
    })
    .catch((e) => {
      throw new Error(e);
    });
};

export const down = (db) => {
  return db.runSql(`TRUNCATE ${tableName};`);
};

export const _meta = {
  version: 1,
};
