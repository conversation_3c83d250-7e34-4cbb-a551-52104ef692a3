'use strict';

let type;

/**
 * We receive the dbmigrate dependency from dbmigrate initially.
 * This enables us to not have to rely on NODE_PATH.
 */
export const setup = (options) => {
  type = options.dbmigrate.dataType;
};

export const up = (db) =>
  db.addColumn('holdings', 'dividendTaxRate', {
    after: 'dividendFrequency',
    precision: '3',
    scale: '2',
    type: type.DECIMAL,
  });

export const down = (db) => db.removeColumn('holdings', 'dividendTaxRate');

export const _meta = {
  version: 1,
};
