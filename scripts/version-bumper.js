#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Function to read package.json
function readPackageJson() {
  const packageJsonPath = path.join(process.cwd(), 'package.json');
  const packageJsonContent = fs.readFileSync(packageJsonPath, 'utf8');
  return JSON.parse(packageJsonContent);
}

// Function to write package.json
function writePackageJson(packageJson) {
  const packageJsonPath = path.join(process.cwd(), 'package.json');
  fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2) + '\n');
}

// Function to bump version
function bumpVersion(version, type) {
  const parts = version.split('.');
  
  if (type === 'minor') {
    parts[1] = (parseInt(parts[1], 10) + 1).toString();
    parts[2] = '0';
  } else if (type === 'patch') {
    parts[2] = (parseInt(parts[2], 10) + 1).toString();
  }
  
  return parts.join('.');
}

// Function to temporarily unprotect/protect the branch
function toggleBranchProtection(projectId, branch, protect) {
  const action = protect ? 'protect' : 'unprotect';
  console.log(`${action}ing branch ${branch}...`);
  
  const curlCommand = `
    curl --request ${protect ? 'POST' : 'DELETE'} \
    --header "PRIVATE-TOKEN: ${process.env.CI_VERSION_BUMPER_TOKEN}" \
    "https://gitlab.com/api/v4/projects/${projectId}/protected_branches${protect ? '' : '/' + encodeURIComponent(branch)}"${
      protect ? ` \
    --header "Content-Type: application/json" \
    --data '{
      "name": "${branch}",
      "push_access_levels": [{"access_level": 40}],
      "merge_access_levels": [{"access_level": 40}]
    }'` : ''
    }
  `;
  
  execSync(curlCommand);
  console.log(`Branch ${branch} ${protect ? 'protected' : 'unprotected'}`);
}

// Add this function after the existing functions
function updateChangelog(newVersion, bumpType, commitMessages) {
  const changelogPath = path.join(process.cwd(), 'CHANGELOG.md');
  let changelog = '';
  
  // Try to read existing changelog
  try {
    if (fs.existsSync(changelogPath)) {
      changelog = fs.readFileSync(changelogPath, 'utf8');
    }
  } catch (error) {
    console.log('No existing CHANGELOG.md found, creating a new one');
  }
  
  // Get the current date in YYYY-MM-DD format
  const today = new Date().toISOString().split('T')[0];
  
  // Create the new changelog entry
  let changelogEntry = `## [${newVersion}] - ${today}\n\n`;
  
  // Add appropriate section based on bump type
  if (bumpType === 'minor') {
    changelogEntry += '### New Features\n\n';
  } else {
    changelogEntry += '### Bug Fixes and Improvements\n\n';
  }
  
  // Add commit messages as bullet points, filtering out merge commits and version bump commits
  const relevantCommits = commitMessages.filter(msg => 
    !msg.startsWith('Merge ') && 
    !msg.startsWith('Bump version to')
  );
  
  if (relevantCommits.length > 0) {
    relevantCommits.forEach(commit => {
      changelogEntry += `- ${commit}\n`;
    });
  } else {
    changelogEntry += '- Various updates and improvements\n';
  }
  
  changelogEntry += '\n';
  
  // If this is a new changelog, add a header
  if (!changelog) {
    changelog = '# Changelog\n\nAll notable changes to this project will be documented in this file.\n\n';
  }
  
  // Insert the new entry after the header
  const headerEndIndex = changelog.indexOf('\n\n') + 2;
  const updatedChangelog = 
    changelog.substring(0, headerEndIndex) + 
    changelogEntry + 
    changelog.substring(headerEndIndex);
  
  // Write the updated changelog
  fs.writeFileSync(changelogPath, updatedChangelog);
  console.log(`Updated CHANGELOG.md for version ${newVersion}`);
  
  return changelogPath;
}

// Main function
function main() {
  try {
    // Get the source branch name from GitLab CI environment variable
    const sourceBranch = process.env.CI_COMMIT_REF_NAME;
    
    if (!sourceBranch) {
      console.error('Source branch name not found in environment variables');
      process.exit(1);
    }
    
    console.log(`Source branch: ${sourceBranch}`);
    
    let bumpType = 'patch'; // Default to patch

    // Check merge request title/description if available
    if (process.env.CI_MERGE_REQUEST_SOURCE_BRANCH_NAME) {
      const mrSourceBranch = process.env.CI_MERGE_REQUEST_SOURCE_BRANCH_NAME;
      console.log(`Merge request source branch: ${mrSourceBranch}`);
      
      if (mrSourceBranch.startsWith('feature/')) {
        console.log('Feature branch detected from merge request source');
        bumpType = 'minor';
      }
    } else {
      try {
        const commitMessage = execSync('git log -1 --pretty=%B').toString().trim();
        console.log(`Commit message: ${commitMessage}`);
        
        // Look for patterns like "Merge branch 'feature/xyz'" in the commit message
        if (commitMessage.includes("Merge branch 'feature/") || 
            commitMessage.includes('Merge branch "feature/') ||
            commitMessage.includes('from feature/')) {
          console.log('Feature branch detected from commit message');
          bumpType = 'minor';
        }
      } catch (e) {
        console.log('Could not determine branch type from commit message');
      }
    }
    
    console.log(`Bump type: ${bumpType}`);
    
    // Get commit messages since the last version bump
    let commitMessages = [];
    try {
      // Find the last version bump commit
      const lastVersionBumpCmd = "git log --grep='Bump version to' -1 --format=%H";
      const lastVersionBumpCommit = execSync(lastVersionBumpCmd).toString().trim();
      
      // If found, get all commit messages since then
      if (lastVersionBumpCommit) {
        const commitsCmd = `git log ${lastVersionBumpCommit}..HEAD --pretty=format:%s`;
        commitMessages = execSync(commitsCmd).toString().split('\n').filter(Boolean);
      }
    } catch (e) {
      // If no previous version bump found, get the last 10 commits
      console.log('No previous version bump found, using recent commits');
      const commitsCmd = 'git log -10 --pretty=format:%s';
      commitMessages = execSync(commitsCmd).toString().split('\n').filter(Boolean);
    }

    console.log(`Found ${commitMessages.length} commits for changelog`);
    
    // Read package.json
    const packageJson = readPackageJson();
    const currentVersion = packageJson.version;
    console.log(`Current version: ${currentVersion}`);
    
    // Bump version
    const newVersion = bumpVersion(currentVersion, bumpType);
    console.log(`New version: ${newVersion}`);
    
    // Update package.json
    packageJson.version = newVersion;
    writePackageJson(packageJson);
    
    // Configure Git
    execSync('git config --global user.email "<EMAIL>"');
    execSync('git config --global user.name "GitLab CI"');
    
    const repoUrl = process.env.CI_REPOSITORY_URL;
    const repoPath = repoUrl.replace(/^https:\/\/.*?@/, '').replace(/^https:\/\//, '');
    const tokenizedRepoUrl = `https://oauth2:${process.env.CI_VERSION_BUMPER_TOKEN}@${repoPath}`;
    
    console.log(`Using repository: ${repoPath}`);
    
    // Get project ID and branch name
    const projectId = process.env.CI_PROJECT_ID;
    const branch = process.env.CI_DEFAULT_BRANCH || 'master';
    
    // Unprotect branch
    toggleBranchProtection(projectId, branch, false);
    
    // Update the changelog
    const changelogPath = updateChangelog(newVersion, bumpType, commitMessages);
    
    // Commit and push the version change
    execSync('git add package.json');
    execSync(`git add ${changelogPath}`);
    execSync(`git commit -m "Bump version to ${newVersion}"`);
    execSync(`git push ${tokenizedRepoUrl} HEAD:${branch}`);
    
    console.log('Version bump completed successfully');
    
    // Protect branch again
    toggleBranchProtection(projectId, branch, true);
    
  } catch (error) {
    // Try to re-protect the branch even if there was an error
    try {
      const projectId = process.env.CI_PROJECT_ID;
      const branch = process.env.CI_DEFAULT_BRANCH || 'master';
      toggleBranchProtection(projectId, branch, true);
    } catch (protectError) {
      console.error('Error re-protecting branch:', protectError);
    }
    
    console.error('Error bumping version:', error);
    process.exit(1);
  }
}

main(); 