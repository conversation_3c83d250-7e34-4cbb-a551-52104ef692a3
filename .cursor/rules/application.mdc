---
description: 
globs: 
alwaysApply: true
---
---
description: Node.js and Express.js best practices for backend development
globs: /*.js, /*.ts, src/**/*.ts
---

# Application

## Project Structure
- never use relative paths, use path aliases defined in tsconfigs for each project:
  @e2e for files inside /e2e/src
  @api for files inside /src
  @types for files inside /types
- always sort object keys is alphabetical order
- try to sort imports in alphabetical order, but keep in mind third party modules should always be on top followed by empty line then local imports

## Tests
- when creating test file for the first time use bare minimum setup and add only 1 test while planning the rest of the tests in the file with comments/descriptions, this way we can implement tests one by one while making sure they work all the time