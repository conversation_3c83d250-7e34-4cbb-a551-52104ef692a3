# Monthly Currency Rates - Sophisticated Check Logic

## Overview

The monthly currency rates feature includes sophisticated checks to determine when to save historical monthly snapshots of currency rates. The system performs two critical checks before saving monthly data:

1. **Last Day of Month Check** - Ensures we only save monthly rates on the actual last day of each month
2. **USA Exchange Hours Check** - Ensures we only save rates when NASDAQ is closed to get final daily rates

## Implementation Details

### Files Created/Modified

- **`src/lib/utils/market-utils.ts`** - Core utility functions for market and date checks
- **`src/controllers/task/index.ts`** - Updated daily task to include sophisticated checks
- **`e2e/src/lib/utils/market-utils.test.ts`** - Comprehensive tests for market utilities

### Core Functions

#### `isLastDayOfMonth(date?: Date): boolean`
- Checks if the given date is the last day of the month
- Works correctly for leap years (Feb 29) and months with different day counts
- Uses absolute date calculation without timezone considerations

#### `areUSAExchangesClosed(date?: Date): boolean`
- Checks if NASDAQ/USA exchanges are closed
- **Market Hours**: 9:30 AM - 4:00 PM Eastern Time (Monday-Friday)
- Returns `true` if:
  - It's a weekend (Saturday/Sunday)
  - It's before 9:30 AM ET
  - It's after 4:00 PM ET

#### `shouldSaveMonthlyRates(date?: Date): object`
- Combines both checks and returns comprehensive status
- Returns object with:
  ```typescript
  {
    isLastDayOfMonth: boolean,
    areExchangesClosed: boolean,
    shouldSave: boolean,           // true only if both conditions are met
    monthEndDate: string,          // YYYY-MM-DD format
    checkTime: string              // ISO timestamp
  }
  ```

### Task Controller Logic

The daily cron job (runs at 3:00 AM Pacific Time) now:

1. **Performs Market Check**:
   ```typescript
   const marketCheck = shouldSaveMonthlyRates();
   logMarketCheckResults(marketCheck);
   ```

2. **Always Saves to `currency_rates`** (daily rates):
   ```typescript
   const rate = Rate({
     id: currency.id,
     code: currency.code,
     rate: roundedRate,
   });
   savePromises.push(rate.save());
   ```

3. **Conditionally Saves to `currency_rates_monthly`**:
   ```typescript
   if (marketCheck.shouldSave) {
     const monthlyRate = RateMonthly({
       currency_id: currency.id,
       month_end_date: marketCheck.monthEndDate,
       rate: roundedRate,
     });
     monthlyRatePromises.push(monthlyRate.save());
   }
   ```

### Example Scenarios

#### ✅ **Will Save Monthly Rates**
- **Date**: February 29, 2024 (last day of month)
- **Time**: 6:00 PM ET (after market close)
- **Result**: Both conditions met → Save monthly rates

#### ❌ **Will NOT Save Monthly Rates**
- **Date**: February 29, 2024 (last day of month)
- **Time**: 2:00 PM ET (during market hours)
- **Result**: Market still open → Skip monthly rates

- **Date**: February 15, 2024 (not last day)
- **Time**: 6:00 PM ET (after market close)
- **Result**: Not last day → Skip monthly rates

### API Endpoints

#### `GET /api/v1/tasks/market-check`
Test endpoint to check current market conditions:
```json
{
  "isLastDayOfMonth": true,
  "areExchangesClosed": true,
  "shouldSave": true,
  "monthEndDate": "2024-02-29",
  "checkTime": "2024-02-29T23:00:00.000Z"
}
```

#### `POST /api/v1/tasks/currencies/update`
Manual trigger endpoint that includes the same sophisticated checks.

### Logging

The system provides detailed logging for transparency:

```
=== Monthly Currency Rates Check ===
Check Time: 2024-02-29T23:00:00.000Z
Is Last Day of Month: true
Are USA Exchanges Closed: true
Month End Date: 2024-02-29
Should Save Monthly Rates: true
=====================================
Saving 10 monthly rates for 2024-02-29
Currency rates saved successfully
```

### Database Schema

Monthly rates are saved to `currency_rates_monthly` table:
- `id` - Auto-increment primary key
- `currency_id` - References currency_rates.id
- `month_end_date` - VARCHAR(10) in YYYY-MM-DD format
- `rate` - DECIMAL(20,10) matching daily rates precision

### Testing

Comprehensive test suite covers:
- Last day detection for all month types (28, 29, 30, 31 days)
- Leap year handling
- Market hours validation across timezones
- Weekend detection
- Integration scenarios

### Benefits

1. **Accurate Historical Data** - Only captures rates when markets are settled
2. **No Duplicates** - Unique constraint prevents duplicate monthly entries
3. **Timezone Aware** - Properly handles Eastern Time for market hours
4. **Flexible** - Easy to modify market hours or add holiday checks
5. **Testable** - All logic is unit tested with various scenarios
6. **Observable** - Detailed logging for monitoring and debugging
