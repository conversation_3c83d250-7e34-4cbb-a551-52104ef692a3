# End-of-Day (EOD) Stock Data Database Setup

This document outlines the database structure for storing and managing NASDAQ EOD stock data.

## Database Schema

### Tables Structure

#### 1. Stocks Table
Stores basic stock information:
```sql
CREATE TABLE stocks (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    symbol VARCHAR(10) NOT NULL,
    name VA<PERSON>HA<PERSON>(255) NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_symbol (symbol)
) ENGINE=MyISAM;
```

#### 2. Stock Prices Table
Stores daily EOD data with partitioning by date:
```sql
CREATE TABLE stock_prices (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    stock_id BIGINT UNSIGNED NOT NULL,
    date DATE NOT NULL,
    open DECIMAL(12,4) NOT NULL,
    high DECIMAL(12,4) NOT NULL,
    low DECIMAL(12,4) NOT NULL,
    close DECIMAL(12,4) NOT NULL,
    adjusted_close DECIMAL(12,4) NOT NULL,
    volume BIGINT UNSIGNED NOT NULL,
    
    INDEX idx_stock_date (stock_id, date),
    INDEX idx_date (date)
) ENGINE=MyISAM 
PARTITION BY RANGE COLUMNS(date) (
    PARTITION p_history VALUES LESS THAN ('2020-01-01'),
    PARTITION p2020 VALUES LESS THAN ('2021-01-01'),
    PARTITION p2021 VALUES LESS THAN ('2022-01-01'),
    PARTITION p2022 VALUES LESS THAN ('2023-01-01'),
    PARTITION p2023 VALUES LESS THAN ('2024-01-01'),
    PARTITION p2024 VALUES LESS THAN ('2025-01-01'),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);
```

#### 3. Corporate Actions Tables

##### Stock Splits
```sql
CREATE TABLE stock_splits (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    stock_id BIGINT UNSIGNED NOT NULL,
    date DATE NOT NULL,
    ratio DECIMAL(10,6) NOT NULL,
    
    INDEX idx_stock_date (stock_id, date)
) ENGINE=MyISAM;
```

##### Dividends
```sql
CREATE TABLE stock_dividends (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    stock_id BIGINT UNSIGNED NOT NULL,
    ex_date DATE NOT NULL,
    payment_date DATE NOT NULL,
    amount DECIMAL(10,4) NOT NULL,
    
    INDEX idx_stock_date (stock_id, ex_date)
) ENGINE=MyISAM;
```

## Important Notes About MyISAM

1. **No Referential Integrity**
   - No foreign key constraints
   - Manual deletion/cleanup required
   - Application must handle data integrity

2. **Table-Level Locking**
   - Better for read-heavy workloads
   - Potential bottlenecks during heavy writes

3. **Performance Benefits**
   - Faster reads
   - Lower memory usage
   - Better for bulk loading

4. **Data Maintenance**
   - Regular OPTIMIZE TABLE recommended
   - Manual cleanup of orphaned records needed
   - Backup/restore operations are simpler

## Maintenance

### Adding New Year Partitions

To add a partition for a new year:

```sql
ALTER TABLE stock_prices 
REORGANIZE PARTITION p_future INTO (
    PARTITION p2025 VALUES LESS THAN ('2026-01-01'),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);
```

### Table Optimization

Regular maintenance for MyISAM tables:

```sql
OPTIMIZE TABLE stocks, stock_prices, stock_splits, stock_dividends;
```

## Query Examples

### 1. Basic EOD Data Query
```sql
SELECT 
    s.symbol,
    sp.date,
    sp.open,
    sp.high,
    sp.low,
    sp.close,
    sp.adjusted_close,
    sp.volume
FROM stocks s
JOIN stock_prices sp ON sp.stock_id = s.id
WHERE s.symbol = 'AAPL' 
AND sp.date = '2024-12-31';
```

### 2. EOD Data with Corporate Actions
```sql
SELECT 
    s.symbol,
    s.name,
    sp.date,
    sp.close,
    sp.adjusted_close,
    (SELECT amount 
     FROM stock_dividends sd 
     WHERE sd.stock_id = s.id 
     AND sd.ex_date <= '2024-12-31'
     ORDER BY sd.ex_date DESC 
     LIMIT 1) as latest_dividend
FROM stocks s
JOIN stock_prices sp ON sp.stock_id = s.id
WHERE s.symbol = 'AAPL' 
AND sp.date = '2024-12-31';
```

### 3. Cleanup Orphaned Records
```sql
-- Delete price records for non-existent stocks
DELETE FROM stock_prices 
WHERE stock_id NOT IN (SELECT id FROM stocks);

-- Delete corporate actions for non-existent stocks
DELETE FROM stock_splits 
WHERE stock_id NOT IN (SELECT id FROM stocks);

DELETE FROM stock_dividends 
WHERE stock_id NOT IN (SELECT id FROM stocks);
```

## Design Considerations

1. **MyISAM Benefits**
   - Faster read performance
   - Lower memory footprint
   - Simpler backup/restore
   - No transaction overhead

2. **Data Integrity**
   - Must be handled at application level
   - Regular cleanup jobs needed
   - Consider periodic data validation

3. **Indexing Strategy**
   - More important with MyISAM
   - Optimize for read performance
   - Regular index maintenance

## Best Practices

1. Regular table optimization
2. Implement application-level integrity checks
3. Schedule periodic cleanup jobs
4. Monitor table fragmentation
5. Regular backups
6. Validate data consistency

## Performance Considerations

1. **Read Performance**
   - MyISAM excels at read operations
   - Full table scans are faster
   - Better query cache utilization

2. **Write Operations**
   - Table-level locking may impact concurrent writes
   - Bulk operations are efficient
   - Consider off-peak hours for updates

3. **Optimization Tips**
   - Use batch operations when possible
   - Monitor lock contention
   - Consider read replicas for reporting

## Data Sources

### 1. Bulk Data Downloads
Several sources provide free EOD data downloads:

#### NASDAQ
- Daily listings and corporate actions
- FTP access: ftp.nasdaqtrader.com
- Files updated nightly
- Includes symbol changes and delistings
- Available formats: pipe-delimited text files
- Complete historical data available

#### NYSE
- Daily price and volume data
- Available through NYSE website
- Requires basic registration
- Historical data available
- Available formats: CSV
- Includes corporate actions

#### Yahoo Finance (Unofficial)
- Bulk downloads available
- Complete historical data
- Available formats: CSV
- Includes splits and dividends
- No registration required
- Can be automated with scripts

#### Stooq.com
- Free historical data downloads
- Daily, weekly, monthly data
- Available formats: CSV, ASCII
- Multiple exchanges coverage
- No registration required
- Includes fundamental data

#### Bulk Processing Strategy
1. Download full dataset monthly
2. Process incrementally using daily updates
3. Store locally for quick access
4. Maintain own historical database

### 2. API Options (Backup Only)
Keep Alpha Vantage or MarketStack API access as backup for:
- Data validation
- Gap filling
- Symbol verification
- Real-time checks when needed

## Data Loading Approach

### Initial Load
1. Download complete historical datasets
2. Process corporate actions first
3. Load price history in batches
4. Validate data integrity

### Daily Updates
1. Download daily summary files (after market close)
2. Process corporate actions
3. Update price records
4. Run integrity checks
5. Store raw files for backup

### Storage Considerations
1. Keep raw downloaded files
2. Implement versioning for data files
3. Maintain checksums for validation
4. Compress historical data



