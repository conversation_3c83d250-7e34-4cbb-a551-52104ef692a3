import bcrypt from 'bcrypt';

export interface UserMockInterface {
  (data: any): any;
  findOne: jest.<PERSON>ck;
}

export interface GoalMockInterface {
  (data: any): any;
  findAll: jest.Mock;
  findOne: jest.Mock;
  deleteAll: jest.Mock;
  delete: jest.Mock;
}

export interface HoldingMockInterface {
  (data: any): any;
  findAll: jest.Mock;
  findOne: jest.Mock;
  deleteAll: jest.Mock;
  delete: jest.Mock;
}

export interface RateMockInterface {
  findAll: jest.Mock;
}

export const createMockUser = (overrides = {}) => {
  const defaultUserData = {
    id: '123',
    email: '<EMAIL>',
    pass: bcrypt.hashSync('password123', 12),
    dtLogin: Math.floor(Date.now() / 1000),
    dtCreated: Math.floor(Date.now() / 1000),
    currency: 1,
    licence: 'free',
    isNew: false,
    jwt: 'mock-jwt-token',
    otp: '',
    otpExpires: 0,
  };

  const userData = { ...defaultUserData, ...overrides };

  return {
    ...userData,
    save: jest.fn().mockResolvedValue(userData),
    toJson: jest.fn().mockReturnValue({
      id: userData.id,
      email: userData.email,
      dtLogin: userData.dtLogin,
      dtCreated: userData.dtCreated,
      currency: userData.currency,
      licence: userData.licence,
    }),
  };
};

export const createMockGoal = (data = {}, overrides = {}) => {
  const goalData = {
    id: '1',
    uid: 'test-user',
    currency: 1,
    dividends: 100,
    networth: 1000,
    ...data,
  };

  const instance = {
    ...goalData,
    save: jest.fn().mockResolvedValue(goalData),
    import: jest.fn().mockResolvedValue(goalData),
    toJson: () => goalData,
    isNew: !goalData.id,
    ...overrides,
  };

  // Make isNew non-enumerable
  Object.defineProperty(instance, 'isNew', {
    enumerable: false,
    value: !goalData.id,
  });

  return instance;
};

export const createMockErrorCode = ({ code, message }: { code: string; message?: string }) => ({
  code,
  message: message || `Error code: ${code}`,
});

export const createMockRate = (data = {}) => ({
  currency: 'USD',
  rate: 1,
  ...data,
});

export const createMockHolding = (data = {}, overrides = {}) => {
  const holdingData = {
    id: '1',
    uid: 'test-user',
    count: 10,
    currency: 1,
    dividend: 1.5,
    dividendCurrency: 1,
    dividendFrequency: 4,
    isDeleted: false,
    name: 'Test Stock',
    position: 1,
    price: 100,
    symbol: 'TEST',
    ...data,
  };

  const instance = {
    ...holdingData,
    save: jest.fn().mockResolvedValue(holdingData),
    import: jest.fn().mockResolvedValue(holdingData),
    toJson: () => holdingData,
    isNew: !holdingData.id,
    ...overrides,
  };

  // Make isNew non-enumerable
  Object.defineProperty(instance, 'isNew', {
    enumerable: false,
    value: !holdingData.id,
  });

  return instance;
};

export const mockUserModel = () => {
  const UserMock = Object.assign(
    jest.fn().mockImplementation((data) => createMockUser(data)),
    {
      findOne: jest.fn().mockImplementation(() => Promise.resolve(null)),
      delete: jest.fn().mockImplementation(() => Promise.resolve()),
    },
  ) as UserMockInterface;

  return UserMock;
};

export const mockGoalModel = () => {
  const GoalMock = Object.assign(
    jest.fn().mockImplementation((data) => createMockGoal(data)),
    {
      findAll: jest.fn().mockImplementation(() => Promise.resolve([])),
      findOne: jest.fn().mockImplementation(() => Promise.resolve(null)),
      deleteAll: jest.fn().mockImplementation(() => Promise.resolve()),
      delete: jest.fn().mockImplementation(() => Promise.resolve()),
    },
  ) as GoalMockInterface;

  return GoalMock;
};

export const mockHoldingModel = () => {
  const HoldingMock = Object.assign(
    jest.fn().mockImplementation((data) => createMockHolding(data)),
    {
      findAll: jest.fn().mockImplementation(() => Promise.resolve([])),
      findOne: jest.fn().mockImplementation(() => Promise.resolve(null)),
      deleteAll: jest.fn().mockImplementation(() => Promise.resolve()),
      delete: jest.fn().mockImplementation(() => Promise.resolve()),
    },
  ) as HoldingMockInterface;

  return HoldingMock;
};

export const mockRateModel = () => {
  const defaultRates = [createMockRate({ currency: 'USD', rate: 1 }), createMockRate({ currency: 'EUR', rate: 0.85 })];

  return {
    findAll: jest.fn().mockImplementation(() => Promise.resolve(defaultRates)),
  } as RateMockInterface;
};

export const mockErrorCodeModel = () => jest.fn(({ code, message }) => createMockErrorCode({ code, message }));
