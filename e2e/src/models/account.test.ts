jest.mock('@api/connection');
import db from '@api/connection';
import AccountModel from '@api/models/account';
import { toJson } from '@api/lib/utils';
import { escapeKey, getKeys, sqlWhere } from '@api/lib/test-helpers';
import { ACCOUNT_TYPES, ACCOUNT_TAX_TREATMENTS } from '@api/lib/constants/accounts';

const createModelObject = (attrs = {}) => ({
  id: 1,
  type: ACCOUNT_TYPES.SELF_DIRECTED,
  taxTreatment: ACCOUNT_TAX_TREATMENTS.TAXABLE,
  withholdingTax: 15,
  name: 'Test Investment Account',
  description: 'A test investment account',
  uid: 'test-user-id',

  ...attrs,
});

describe('Models | Account', () => {
  const table = 'accounts';
  let instance: AccountI;

  beforeEach(() => {
    instance = AccountModel(createModelObject());
  });

  afterEach(() => {
    jest.restoreAllMocks();
    // @ts-expect-error - expected
    db.query.mockClear(); // reset mock call count
  });

  it('should be defined', () => {
    expect(AccountModel).toBeDefined();
    expect(instance).toBeDefined();
  });

  it('should mark an instance without id as new', () => {
    const name = 'New Account';
    const accountA = AccountModel({ name });
    const accountB = AccountModel({ id: 1, name });

    expect(accountA.isNew).toBe(true);
    expect(accountB.isNew).toBe(false);
  });

  it('should not list special attributes', () => {
    expect(instance).toEqual(createModelObject());
    expect(instance.toJson()).toEqual(createModelObject());
  });

  it('should use a nonenumerable method to return JSON structure of the model', () => {
    expect(instance.toJson()).toEqual(toJson(instance));
  });

  it('should use a nonenumerable method "save" for new and existing models', async () => {
    const name = 'Test Account';
    const modelNew = AccountModel({ name });
    const modelExisting = AccountModel(createModelObject());

    try {
      await modelNew.save();
      await modelExisting.save();
    } catch (error) {
      console.log(error);
    }

    // For new model, check database insert with camelCase column names
    expect(db.query).toHaveBeenCalledTimes(2);
    expect(db.query).toHaveBeenNthCalledWith(
      1,
      expect.stringContaining('INSERT INTO accounts'),
      expect.arrayContaining(['Test Account']),
      expect.any(Function),
    );
    expect(db.query).toHaveBeenNthCalledWith(2, expect.stringContaining('UPDATE accounts'), expect.any(Array), expect.any(Function));
  });

  it('should execute a correct query for static method "findOne"', async () => {
    const id = 1;

    try {
      await AccountModel.findOne({ where: { id } });
    } catch (error) {
      console.log(error);
    }

    expect(db.query).toHaveBeenCalledTimes(1);
    expect(db.query).toHaveBeenNthCalledWith(1, `SELECT * FROM ${table} ${sqlWhere({ id })} LIMIT 1;`, [id], expect.any(Function));
  });

  it('should execute a correct query for static method "findAll"', async () => {
    try {
      await AccountModel.findAll({});
    } catch (error) {
      console.log(error);
    }

    expect(db.query).toHaveBeenCalledTimes(1);
    expect(db.query).toHaveBeenNthCalledWith(1, `SELECT * FROM ${table};`, [], expect.any(Function));
  });

  it('should execute a correct query for static method "delete"', async () => {
    const { id, uid } = instance;

    try {
      await AccountModel.delete({ id, uid });
    } catch (error) {
      console.log(error);
    }

    expect(db.query).toHaveBeenCalledTimes(1);
    expect(db.query).toHaveBeenNthCalledWith(1, `DELETE FROM ${table} ${sqlWhere({ id, uid })};`, [id, uid], expect.any(Function));
  });

  it('should execute a correct query for static method "deleteAll"', async () => {
    const { uid } = instance;

    try {
      await AccountModel.deleteAll(uid);
    } catch (error) {
      console.log(error);
    }

    expect(db.query).toHaveBeenCalledTimes(1);
    expect(db.query).toHaveBeenNthCalledWith(1, `DELETE FROM ${table} ${sqlWhere({ uid })};`, [uid], expect.any(Function));
  });

  it('should use default values for account type and tax treatment', () => {
    const accountWithDefaults = AccountModel({ name: 'Test Account' });

    expect(accountWithDefaults.type).toBe(ACCOUNT_TYPES.SELF_DIRECTED);
    expect(accountWithDefaults.taxTreatment).toBe(ACCOUNT_TAX_TREATMENTS.TAXABLE);
  });

  it('should validate account type', () => {
    const validAccount = AccountModel({
      name: 'Test Account',
      type: ACCOUNT_TYPES.RETIREMENT,
    });

    expect(validAccount.type).toBe(ACCOUNT_TYPES.RETIREMENT);
  });

  it('should validate tax treatment', () => {
    const validAccount = AccountModel({
      name: 'Test Account',
      taxTreatment: ACCOUNT_TAX_TREATMENTS.TAX_FREE,
    });

    expect(validAccount.taxTreatment).toBe(ACCOUNT_TAX_TREATMENTS.TAX_FREE);
  });
});
