jest.mock('@api/connection');
import db from '@api/connection';
import UserModel from '@api/models/user';
import { CURRENCY_CAD } from '@api/lib/constants/currencies';
import { toJson } from '@api/lib/utils';
import { escapeKey, getKeys, sqlParams, sqlWhere } from '@api/lib/test-helpers';

const createModelObject = (attrs: Partial<UserI> = {}) =>
  ({
    id: 'test-a',
    currency: CURRENCY_CAD,
    dtCreated: Math.floor(new Date('2019-01-02').valueOf() / 1000),
    dtLogin: Math.floor(new Date('2019-01-03').valueOf() / 1000),
    email: '<EMAIL>',
    jwt: 'jwt-string',
    licence: null,
    otp: null,
    otpExpires: null,
    pass: 'attr-pass',

    ...attrs,
  }) as UserI;

describe('Models | User', () => {
  const table = 'users';
  const hidden = ['jwt', 'otp', 'otpExpires', 'pass'];
  let instance: UserI;

  beforeEach(() => {
    instance = UserModel(createModelObject());
  });

  afterEach(() => {
    jest.restoreAllMocks();
    // @ts-expect-error - expected
    db.query.mockClear(); // reset mock call count
  });

  it('should be defined', () => {
    expect(UserModel).toBeDefined();
    expect(instance).toBeDefined();
  });

  it('should mark an instance without id as new', () => {
    const email = '<EMAIL>';
    const userA = UserModel({ id: undefined, email });
    const userB = UserModel({ id: '1', email });

    expect(userA.isNew).toBe(true);
    expect(userB.isNew).toBe(false);
  });

  it('should not list special attributes', () => {
    expect(instance).toEqual({ ...createModelObject() });
    expect(instance.toJson()).toEqual(toJson(createModelObject(), { hidden }));
  });

  it('should use a nonenumerable method to return JSON structure of the model', () => {
    expect(instance.toJson()).toEqual(toJson(instance, { hidden }));
  });

  it('should use a nonenumerable method "save" for new and existing models', async () => {
    const email = '<EMAIL>';
    const pass = 'testpass';
    const userNew = UserModel({ email, pass });
    const userExisting = UserModel(createModelObject());

    try {
      await userNew.save();
      await userExisting.save();
    } catch (error) {
      console.log(error);
    }

    const newkeys = getKeys(userNew);
    const dataQuery = Object.keys(userExisting)
      .map((key) => `${escapeKey(key)}=?`)
      .join(', ');

    expect(db.query).toHaveBeenCalledTimes(2);
    expect(db.query).toHaveBeenNthCalledWith(
      1,
      `INSERT INTO ${table} (${newkeys}) VALUES (${Object.values(userNew)
        .map(() => '?')
        .join(', ')});`,
      sqlParams(userNew),
      expect.any(Function),
    );
    expect(db.query).toHaveBeenNthCalledWith(
      2,
      `UPDATE ${table} SET ${dataQuery} ${sqlWhere({ id: userExisting.id })};`,
      [...sqlParams(userExisting), userExisting.id],
      expect.any(Function),
    );
  });

  it('should execute a correct query for static method "findOne"', async () => {
    const id = 'test-id';
    const email = '<EMAIL>';

    try {
      await UserModel.findOne({ where: { id } });
      await UserModel.findOne({ where: { id, email } });
    } catch (error) {
      console.log(error);
    }

    expect(db.query).toHaveBeenCalledTimes(2);
    expect(db.query).toHaveBeenNthCalledWith(1, `SELECT * FROM ${table} ${sqlWhere({ id })} LIMIT 1;`, [id], expect.any(Function));
    expect(db.query).toHaveBeenNthCalledWith(
      2,
      `SELECT * FROM ${table} ${sqlWhere({ id, email })} LIMIT 1;`,
      [id, email],
      expect.any(Function),
    );
  });

  it('should execute a correct query for static method "delete"', async () => {
    const id = 'test-id';

    try {
      await UserModel.delete(id);
    } catch (error) {
      console.log(error);
    }

    expect(db.query).toHaveBeenCalledTimes(1);
    expect(db.query).toHaveBeenNthCalledWith(1, `DELETE FROM ${table} ${sqlWhere({ id })};`, [id], expect.any(Function));
  });
});
