jest.mock('@api/connection');
import db from '@api/connection';
import HoldingModel from '@api/models/holding';
import { toJson } from '@api/lib/utils';
import { escapeKey, getKeys, sqlWhere } from '@api/lib/test-helpers';

const createModelObject = (attrs = {}) => ({
  id: 'test-id',
  uid: 'test-user-id',
  symbol: 'ALA',
  currency: 2,
  name: 'Altagas',
  count: 13,
  price: 20.02,
  dividend: 0.08,
  dividendCurrency: 2,
  dividendFrequency: 1,
  dividendTaxRate: 0.15,
  position: 0,
  isDeleted: false,

  ...attrs,
});

describe('Models | Holding', () => {
  const table = 'holdings';
  let instance: HoldingI;

  beforeEach(() => {
    instance = HoldingModel(createModelObject());
  });

  afterEach(() => {
    jest.restoreAllMocks();
    // @ts-expect-error - expected
    db.query.mockClear(); // reset mock call count
  });

  it('should be defined', () => {
    expect(HoldingModel).toBeDefined();
    expect(instance).toBeDefined();
  });

  it('should mark an instance without id as new', () => {
    const symbol = 'AAA';
    const holdingA = HoldingModel({ symbol });
    const holdingB = HoldingModel({ id: '1', symbol });

    expect(holdingA.isNew).toBe(true);
    expect(holdingB.isNew).toBe(false);
  });

  it('should not list special attributes', () => {
    expect(instance).toEqual(createModelObject());
    expect(instance.toJson()).toEqual(createModelObject());
  });

  it('should use a nonenumerable method to return JSON structure of the model', () => {
    expect(instance.toJson()).toEqual(toJson(instance, { hidden: ['pass'] }));
  });

  it('should use a nonenumerable method "save" for new and existing models', async () => {
    const uid = 'test-uid-1';
    const modelNew = HoldingModel({ uid });
    const modelExisting = HoldingModel(createModelObject());

    try {
      await modelNew.save();
      await modelExisting.save();
    } catch (error) {
      console.log(error);
    }

    const newkeys = getKeys(modelNew);
    const newReplacements = Object.values(modelNew)
      .map(() => '?')
      .join(', ');
    const dataQuery = Object.keys(modelExisting)
      .map((key) => `${escapeKey(key)}=?`)
      .join(', ');

    expect(db.query).toHaveBeenCalledTimes(2);
    expect(db.query).toHaveBeenNthCalledWith(
      1,
      `INSERT INTO ${table} (${newkeys}) VALUES (${newReplacements});`,
      Object.values(modelNew),
      expect.any(Function),
    );
    expect(db.query).toHaveBeenNthCalledWith(
      2,
      `UPDATE ${table} SET ${dataQuery} ${sqlWhere({ id: modelExisting.id, uid: modelExisting.uid })};`,
      [...Object.values(modelExisting), modelExisting.id, modelExisting.uid],
      expect.any(Function),
    );
  });

  it('should execute a correct query for static method "findOne"', async () => {
    const id = 'test-id';

    try {
      await HoldingModel.findOne({ where: { id } });
    } catch (error) {
      console.log(error);
    }

    expect(db.query).toHaveBeenCalledTimes(1);
    expect(db.query).toHaveBeenNthCalledWith(1, `SELECT * FROM ${table} ${sqlWhere({ id })} LIMIT 1;`, [id], expect.any(Function));
  });

  it('should execute a correct query for static method "findAll"', async () => {
    const uid = 'test-id';

    try {
      await HoldingModel.findAll({ where: { uid } });
    } catch (error) {
      console.log(error);
    }

    expect(db.query).toHaveBeenCalledTimes(1);
    expect(db.query).toHaveBeenNthCalledWith(1, `SELECT * FROM ${table} ${sqlWhere({ uid })};`, [uid], expect.any(Function));
  });

  it('should execute a correct query for static method "delete"', async () => {
    const { id, uid } = instance;

    try {
      await HoldingModel.delete(instance);
    } catch (error) {
      console.log(error);
    }

    expect(db.query).toHaveBeenCalledTimes(1);
    expect(db.query).toHaveBeenNthCalledWith(1, `DELETE FROM ${table} ${sqlWhere({ id, uid })};`, [id, uid], expect.any(Function));
  });

  // deleteAll
  it('should execute a correct query for static method "deleteAll"', async () => {
    const { uid } = instance;

    try {
      await HoldingModel.deleteAll(uid);
    } catch (error) {
      console.log(error);
    }

    expect(db.query).toHaveBeenCalledTimes(1);
    expect(db.query).toHaveBeenNthCalledWith(1, `DELETE FROM ${table} ${sqlWhere({ uid })};`, [uid], expect.any(Function));
  });

  // saveAll
  // it('should execute a correct query for static method "saveAll"', async () => {
  //   const uid = 'test-uid-1';
  //   const modelA = HoldingModel(createModelObject({ id: 'test-id-1', uid }));
  //   const modelB = HoldingModel(createModelObject({ id: 'test-id-2', uid }));

  //   try {
  //     await HoldingModel.saveAll2([modelA, modelB]);
  //   } catch (error) {
  //     console.log(error);
  //   }

  //   expect(db.query).toHaveBeenCalledTimes(1);
  //   expect(db.query).toHaveBeenNthCalledWith(1, `DELETE FROM ${table} ${sqlWhere({ uid })};`, [uid], expect.any(Function));
  // });

  // it('should throw an error for static method "saveAll" without uid', async () => {
  //   try {
  //     await HoldingModel.saveAll();
  //   } catch (error) {
  //     expect(error.message).toBe('SaveAll method expected an array as parameter.');
  //   }
  // });
});
