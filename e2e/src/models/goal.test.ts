jest.mock('@api/connection');
import db from '@api/connection';
import GoalModel from '@api/models/goal';
import { toJson } from '@api/lib/utils';
import { escapeKey, getKeys, sqlWhere } from '@api/lib/test-helpers';
import { CURRENCY_USD } from '@api/lib/constants/currencies';

const createModelObject = (attrs = {}) => ({
  id: 'test-a',
  uid: 'test-user-id',
  currency: 1,
  dividends: 5000,
  networth: 25000,

  ...attrs,
});

describe('Models | Goal', () => {
  const table = 'goals';
  let instance: GoalI;

  beforeEach(() => {
    instance = GoalModel(createModelObject());
  });

  afterEach(() => {
    jest.restoreAllMocks();

    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    db.query.mockClear(); // reset mock call count
  });

  it('should be defined', () => {
    expect(GoalModel).toBeDefined();
    expect(instance).toBeDefined();
  });

  it('should mark an instance without id as new', () => {
    const currency = CURRENCY_USD;
    const goalA = GoalModel({ currency });
    const goalB = GoalModel({ id: '1', currency });

    expect(goalA.isNew).toBe(true);
    expect(goalB.isNew).toBe(false);
  });

  it('should not list special attributes', () => {
    expect(instance).toEqual(createModelObject());
    expect(instance.toJson()).toEqual(createModelObject());
  });

  it('should use a nonenumerable method to return JSON structure of the model', () => {
    expect(instance.toJson()).toEqual(toJson(instance, { hidden: ['pass'] }));
  });

  it('should use correctly a nonenumerable method "save" for new models', async () => {
    const uid = 'test-uid-1';
    const model = GoalModel({ uid });

    await model.save();

    const newkeys = getKeys(model);
    const newValues = Object.values(model)
      .map(() => '?')
      .join(', ');

    expect(db.query).toHaveBeenCalledTimes(1);
    expect(db.query).toHaveBeenCalledWith(
      `INSERT INTO ${table} (${newkeys}) VALUES (${newValues});`,
      Object.values(model),
      expect.any(Function),
    );
  });

  it('should use correctly a nonenumerable method "save" for existing models', async () => {
    const model = GoalModel(createModelObject());

    await model.save();

    const dataQuery = Object.keys(model)
      .map((key) => `${escapeKey(key)}=?`)
      .join(', ');

    expect(db.query).toHaveBeenCalledTimes(1);
    expect(db.query).toHaveBeenCalledWith(
      `UPDATE ${table} SET ${dataQuery} ${sqlWhere({
        id: model.id,
        uid: model.uid,
      })};`,
      [...Object.values(model), model.id, model.uid],
      expect.any(Function),
    );
  });

  it('should use a nonenumerable method "import"', async () => {
    const uid = 'test-uid-1';
    const model = GoalModel({ uid });

    try {
      await model.import();
    } catch (error) {
      console.log(error);
    }

    const newkeys = getKeys(model);
    const newValues = Object.values(model)
      .map(() => '?')
      .join(', ');

    expect(db.query).toHaveBeenCalledTimes(1);
    expect(db.query).toHaveBeenNthCalledWith(
      1,
      `INSERT INTO ${table} (${newkeys}) VALUES (${newValues});`,
      Object.values(model),
      expect.any(Function),
    );
  });

  it('should execute a correct query for static method "findOne"', async () => {
    const id = 'test-id';
    const uid = 'test-uid';

    try {
      await GoalModel.findOne({ where: { id, uid } });
    } catch (error) {
      console.log(error);
    }

    expect(db.query).toHaveBeenCalledTimes(1);
    expect(db.query).toHaveBeenNthCalledWith(
      1,
      `SELECT * FROM ${table} ${sqlWhere({ id, uid })} LIMIT 1;`,
      [id, uid],
      expect.any(Function),
    );
  });

  it('should execute a correct query for static method "findAll"', async () => {
    const uid = 'test-id';

    try {
      await GoalModel.findAll({ where: { uid } });
    } catch (error) {
      console.log(error);
    }

    expect(db.query).toHaveBeenCalledTimes(1);
    expect(db.query).toHaveBeenNthCalledWith(1, `SELECT * FROM ${table} ${sqlWhere({ uid })};`, [uid], expect.any(Function));
  });

  it('should execute a correct query for static method "delete"', async () => {
    const { id, uid } = instance;

    try {
      await GoalModel.delete(instance);
    } catch (error) {
      console.log(error);
    }

    expect(db.query).toHaveBeenCalledTimes(1);
    expect(db.query).toHaveBeenNthCalledWith(1, `DELETE FROM ${table} ${sqlWhere({ id, uid })};`, [id, uid], expect.any(Function));
  });

  // deleteAll
  it('should execute a correct query for static method "deleteAll"', async () => {
    const { uid } = instance;

    try {
      await GoalModel.deleteAll(uid);
    } catch (error) {
      console.log(error);
    }

    expect(db.query).toHaveBeenCalledTimes(1);
    expect(db.query).toHaveBeenNthCalledWith(1, `DELETE FROM ${table} ${sqlWhere({ uid })};`, [uid], expect.any(Function));
  });
});
