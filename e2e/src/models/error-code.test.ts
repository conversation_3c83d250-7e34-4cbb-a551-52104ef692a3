import ErrorCodeModel from '@api/models/error-code';

describe('Models | ErrorCode', () => {
  it('should create instance', () => {
    const message = 'test message';
    const code = 'test code';

    expect(ErrorCodeModel()).toEqual({ message: '', code: null });
    expect(ErrorCodeModel({ message })).toEqual({ message, code: null });
    expect(ErrorCodeModel({ code })).toEqual({ message: '', code });
    expect(ErrorCodeModel({ message, code })).toEqual({ message, code });
  });
});
