import jwt from 'jsonwebtoken';
import { Request, Response } from 'express';

import AuthService from '@api/services/auth';
import Env from '@api/services/env';
import { User } from '@api/models';
import { ERROR_AUTH } from '@api/lib/constants/error-codes';

// Mock dependencies
jest.mock('jsonwebtoken');
jest.mock('@api/services/env', () => ({
  jwt: {
    secret: 'test-secret',
    expire: '1h',
  },
}));
jest.mock('@api/models', () => ({
  User: {
    findOne: jest.fn(),
  },
  ErrorCode: jest.fn(({ code, message }) => ({
    code,
    message: message || `Error code: ${code}`,
  })),
}));
jest.mock('@api/lib/utils', () => ({
  Response: jest.fn((type, error) => ({
    type,
    error,
  })),
}));

describe('Services | Auth', () => {
  let auth: ReturnType<typeof AuthService>;
  const mockUserId = 'user-123';
  const mockToken = 'valid-token';

  beforeEach(() => {
    auth = AuthService();
    jest.clearAllMocks();
  });

  describe('sign', () => {
    it('should sign a JWT with the correct parameters', () => {
      (jwt.sign as jest.Mock).mockReturnValue(mockToken);

      const result = auth.sign(mockUserId);

      expect(jwt.sign).toHaveBeenCalledWith({ uid: mockUserId }, Env.jwt.secret, { expiresIn: Env.jwt.expire });
      expect(result).toBe(mockToken);
    });
  });

  describe('decode', () => {
    it('should verify and decode a JWT', () => {
      const decodedToken = { uid: mockUserId };
      (jwt.verify as jest.Mock).mockReturnValue(decodedToken);

      const result = auth.decode(mockToken);

      expect(jwt.verify).toHaveBeenCalledWith(mockToken, Env.jwt.secret);
      expect(result).toEqual(decodedToken);
    });
  });

  describe('withAuthMiddleware', () => {
    let mockReq: Partial<Request>;
    let mockRes: Partial<Response>;
    let mockNext: jest.Mock;

    beforeEach(() => {
      mockReq = {
        headers: {
          authorization: `Bearer ${mockToken}`,
        },
        session: {
          cookie: {
            httpOnly: true,
            maxAge: null,
            originalMaxAge: null,
            path: '/',
            signed: false,
          },
          destroy: jest.fn(),
          id: 'session-id',
          regenerate: jest.fn(),
          reload: jest.fn(),
          resetMaxAge: jest.fn(),
          save: jest.fn(),
          touch: jest.fn(),
        },
      };
      mockRes = {
        locals: {},
        status: jest.fn().mockReturnThis(),
        json: jest.fn(),
      };
      mockNext = jest.fn();
    });

    it('should set user data and call next() for valid token', async () => {
      // Mock successful token verification
      (jwt.verify as jest.Mock).mockReturnValue({ uid: mockUserId });

      // Mock successful user lookup
      const mockUser = { id: mockUserId, jwt: mockToken };
      (User.findOne as jest.Mock).mockResolvedValue(mockUser);

      await auth.withAuthMiddleware(mockReq as Request, mockRes as Response, mockNext);

      // Check that user data was set correctly
      expect(mockReq.session?.uid).toBe(mockUserId);
      expect(mockRes.locals?.jwt).toBe(mockToken);
      expect(mockRes.locals?.user).toEqual(mockUser);
      expect(mockRes.locals?.uid).toBe(mockUserId);

      // Check that next() was called
      expect(mockNext).toHaveBeenCalled();

      // Check that error handlers were not called
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should return 401 when token is valid but user not found', async () => {
      // Mock successful token verification
      (jwt.verify as jest.Mock).mockReturnValue({ uid: mockUserId });

      // Mock user not found
      (User.findOne as jest.Mock).mockResolvedValue(null);

      await auth.withAuthMiddleware(mockReq as Request, mockRes as Response, mockNext);

      // Check error response
      expect(mockRes.status).toHaveBeenCalledWith(401);
      expect(mockRes.json).toHaveBeenCalled();
      expect(mockNext).not.toHaveBeenCalled();

      // Check that the error code is correct
      const jsonCall = (mockRes.json as jest.Mock).mock.calls[0][0];
      expect(jsonCall.error.code).toBe(ERROR_AUTH.TOKEN);
    });

    it('should return 401 when token verification fails', async () => {
      // Mock token verification failure
      const verifyError = new Error('Invalid token');
      (jwt.verify as jest.Mock).mockImplementation(() => {
        throw verifyError;
      });

      await auth.withAuthMiddleware(mockReq as Request, mockRes as Response, mockNext);

      // Check error response
      expect(mockRes.status).toHaveBeenCalledWith(401);
      expect(mockRes.json).toHaveBeenCalled();
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle missing authorization header', async () => {
      // Remove authorization header
      mockReq.headers = {};

      await auth.withAuthMiddleware(mockReq as Request, mockRes as Response, mockNext);

      // Check error response
      expect(mockRes.status).toHaveBeenCalledWith(401);
      expect(mockRes.json).toHaveBeenCalled();
      expect(mockNext).not.toHaveBeenCalled();
    });
  });
});
