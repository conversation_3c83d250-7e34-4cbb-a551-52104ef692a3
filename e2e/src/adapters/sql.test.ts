jest.mock('@api/connection');
import db from '@api/connection';
import SqlAdapter, { SqlAdapterI } from '@api/adapters/sql';
import { User } from '@api/models';
import { escapeKey, sqlSelect, sqlWhere, sqlParams, sqlSort } from '@api/lib/test-helpers';

describe('Adapters | SQL', () => {
  const table = 'test_table';
  const errors = {
    constructor: 'SQL Adapter requires TABLE name specified.',
    findOne: 'SQL "Apdater.findOne()" method requires "options" and "where" attribute.',
    create: '"Adapter.create()" method requres "data".',
    update: '"Adapter.update()" method requres "data" and "options.where" arguments.',
    delete: '"Adapter.delete()" method requres "options" and "options.where" arguments.'
  };

  let instance: SqlAdapterI;
  let SELECT: string;

  const params = Object.freeze({
    where: {
      type: 'test',
      name: '<PERSON>'
    },
    sort: 'position'
  });

  beforeEach(() => {
    SELECT = sqlSelect(table);
    instance = SqlAdapter({ table });
  });

  afterEach(() => {
    jest.restoreAllMocks();
    // @ts-expect-error - mockClear is jest fn not db query
    db.query.mockClear(); // reset mock call count
  });

  it('should be defined', () => {
    expect(SqlAdapter).toBeDefined();
    expect(instance).toBeDefined();
  });

  it('should throw an error if no options or no "table" option provided', () => {
    expect.assertions(2);

    try {
      SqlAdapter();
    } catch (error: unknown) {
      const { message } = error as Error;
      expect(message).toBe(errors.constructor);
    }

    try {
      SqlAdapter({});
    } catch (error: unknown) {
      const { message } = error as Error;
      expect(message).toBe(errors.constructor);
    }
  });

  it('should throw an error for "findOne" without "options" or "options.where"', async () => {
    expect.assertions(2);

    try {
      await instance.findOne();
    } catch (error: unknown) {
      const { message } = error as Error;
      expect(message).toBe(errors.findOne);
    }

    try {
      await instance.findOne({});
    } catch (error: unknown) {
      const { message } = error as Error;
      expect(message).toBe(errors.findOne);
    }
  });

  it('should call all necessary functions on db object with correct arguments for "findOne"', async () => {
    expect.assertions(3);
    const id = 'unique-id-test';
    const email = '<EMAIL>';
    const pass = 'test-pass';

    await instance.findOne({ where: { id } });
    await instance.findOne({ where: { email, pass } });
    expect(db.query).toHaveBeenCalledTimes(2);
    expect(db.query).toHaveBeenNthCalledWith(1, `${SELECT} ${sqlWhere({ id })} LIMIT 1;`, [id], expect.any(Function));
    expect(db.query).toHaveBeenNthCalledWith(2, `${SELECT} ${sqlWhere({ email, pass })} LIMIT 1;`, [email, pass], expect.any(Function));
  });

  it('should make a correct SQL query request for simple "findAll"', async () => {
    await instance.findAll();
    expect(db.query).toHaveBeenCalledTimes(1);
    expect(db.query).toHaveBeenCalledWith(`${SELECT};`, [], expect.any(Function));
  });

  it('should make a correct SQL query request for complex "findAll"', async () => {
    const { where, sort } = params;
    const sortDesc = '-position';

    await instance.findAll({ where, sort });
    await instance.findAll({ where, sort: sortDesc });

    expect(db.query).toHaveBeenCalledTimes(2);
    expect(db.query).toHaveBeenNthCalledWith(1, `${SELECT} ${sqlWhere(where)} ${sqlSort(sort)};`, sqlParams(where), expect.any(Function));
    expect(db.query).toHaveBeenNthCalledWith(
      2,
      `${SELECT} ${sqlWhere(where)} ${sqlSort(sortDesc)};`,
      sqlParams(where),
      expect.any(Function)
    );
  });

  it('should create correct SQL for "create"', async () => {
    expect.assertions(1);

    const id = 'unique-id-test';
    const email = '<EMAIL>';
    const pass = 'test-pass';
    const data = { ...User({ id, email, pass }), pass };

    const expectedKeys = Object.keys(data)
      .map((key) => escapeKey(key))
      .join(', ');
    const expectedValues = Object.values(data)
      .map(() => '?')
      .join(', ');
    const expectedQuery = `INSERT INTO ${table} (${expectedKeys}) VALUES (${expectedValues});`;

    await instance.create(data);

    expect(db.query).toHaveBeenCalledWith(expectedQuery, sqlParams(data), expect.any(Function));
  });

  it('should create correct SQL for "update"', async () => {
    expect.assertions(1);

    const id = 'unique-id-test';
    const email = '<EMAIL>';
    const pass = 'test-pass';
    const data = { ...User({ id, email, pass }).toJson(), pass };

    delete data.id;

    const expectedSet = Object.keys(data)
      .map((key) => `${escapeKey(key)}=?`)
      .join(', ');
    const expectedQuery = `UPDATE ${table} SET ${expectedSet} ${sqlWhere({ id })};`;

    await instance.update(data, { where: { id } });

    expect(db.query).toHaveBeenCalledWith(expectedQuery, [...sqlParams(data), id], expect.any(Function));
  });

  it('should create correct SQL for "delete"', async () => {
    expect.assertions(1);

    const id = 'unique-id-test';
    const expectedQuery = `DELETE FROM ${table} ${sqlWhere({ id })};`;

    await instance.delete({ where: { id } });

    expect(db.query).toHaveBeenCalledWith(expectedQuery, [id], expect.any(Function));
  });

  // it('should throw an error if thrown in DB', async (done) => {
  //   expect.assertions(1);

  //   let error = new Error('DB ERROR');

  //   db.query = jest.fn().mockImplementation((query, cb) => {
  //     cb(error, null);
  //   });

  //   let adapterInstance = SqlAdapter({
  //     table
  //   });

  //   params.sort = '-position';

  //   try {
  //     await adapterInstance.findAll(params);
  //   } catch (e) {
  //     expect(e.message).toBe(error.message);
  //     done();
  //   }
  // });
});
