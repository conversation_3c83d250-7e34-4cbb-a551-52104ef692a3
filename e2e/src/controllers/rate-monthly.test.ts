// Mock database connection
jest.mock('@api/connection');

// Mock jsonwebtoken
jest.mock('jsonwebtoken');

import { Express } from 'express';
import { Server } from 'http';
import request from 'supertest';
import db from '@api/connection';

import { Auth } from '@api/services';
import { ERROR_AUTH, ERROR_RATE_MONTHLY } from '@api/lib/constants/error-codes';
import { createTestServer } from '@e2e/support/server-test-utils';

describe('Controllers | RateMonthly', () => {
  let app: Express;
  let server: Server;
  let authToken: string;
  const testUserId = 'test-user';

  beforeAll(async () => {
    const { app: testApp, server: testServer } = createTestServer();
    app = testApp;
    server = testServer;

    const auth = Auth();
    authToken = auth.sign(testUserId);
  });

  afterAll((done) => {
    if (server) {
      server.close(done);
    } else {
      done();
    }
  });

  beforeEach(() => {
    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.restoreAllMocks();
    // @ts-expect-error - mockClear is jest fn not db query
    db.query.mockClear(); // reset mock call count
  });

  describe('GET /api/v1/rates-monthly', () => {
    it('should return a list of monthly currency rates for authenticated user with date parameter', async () => {
      const mockRates = [
        { id: 1, currency_id: 1, month_end_date: '2024-02-29', rate: 1.3456789012 },
        { id: 2, currency_id: 2, month_end_date: '2024-02-29', rate: 0.8765432109 },
        { id: 3, currency_id: 3, month_end_date: '2024-02-29', rate: 1.2345678901 },
      ];

      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('SELECT * FROM users')) {
          cb(null, [{ id: testUserId, jwt: authToken }]);
        } else if (query.includes('SELECT * FROM currency_rates_monthly')) {
          cb(null, mockRates);
        }
      });

      const response = await request(app)
        .get('/api/v1/rates-monthly?date=2024-02-29')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.data).toEqual(mockRates);
      expect(db.query).toHaveBeenCalledTimes(2);
      expect(db.query).toHaveBeenNthCalledWith(
        1,
        'SELECT * FROM users WHERE `id`=? AND `jwt`=? LIMIT 1;',
        [testUserId, authToken],
        expect.any(Function),
      );
      expect(db.query).toHaveBeenNthCalledWith(
        2,
        'SELECT * FROM currency_rates_monthly WHERE `month_end_date`=?;',
        ['2024-02-29'],
        expect.any(Function),
      );
    });

    it('should return empty list when no rates exist for the given date', async () => {
      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('SELECT * FROM users')) {
          cb(null, [{ id: testUserId, jwt: authToken }]);
        } else if (query.includes('SELECT * FROM currency_rates_monthly')) {
          cb(null, []);
        }
      });

      const response = await request(app)
        .get('/api/v1/rates-monthly?date=2024-01-31')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.data).toEqual([]);
      expect(db.query).toHaveBeenCalledTimes(2);
    });

    it('should return 400 when date parameter is missing', async () => {
      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('SELECT * FROM users')) {
          cb(null, [{ id: testUserId, jwt: authToken }]);
        }
      });

      const response = await request(app)
        .get('/api/v1/rates-monthly')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(400);
      expect(response.body.error.code).toBe(ERROR_RATE_MONTHLY.FETCH);
      expect(response.body.error.message).toBe('Date parameter is required');
      expect(db.query).toHaveBeenCalledTimes(1); // Only auth check
    });

    it('should handle database errors', async () => {
      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('SELECT * FROM users')) {
          cb(null, [{ id: testUserId, jwt: authToken }]);
        } else if (query.includes('SELECT * FROM currency_rates_monthly')) {
          cb(new Error('Database connection failed'));
        }
      });

      const response = await request(app)
        .get('/api/v1/rates-monthly?date=2024-02-29')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(500);
      expect(response.body.error.code).toBe(ERROR_RATE_MONTHLY.FETCH);
      expect(response.body.error.message).toBe('Database connection failed');
      expect(db.query).toHaveBeenCalledTimes(2);
    });

    it('should return 401 when no auth token provided', async () => {
      const response = await request(app).get('/api/v1/rates-monthly?date=2024-02-29');

      expect(response.status).toBe(401);
      expect(response.body.error.code).toBe(ERROR_AUTH.TOKEN);
      expect(db.query).not.toHaveBeenCalled();
    });

    it('should return 401 when invalid auth token provided', async () => {
      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('SELECT * FROM users')) {
          // Return empty array to simulate invalid token
          cb(null, []);
        }
      });

      const response = await request(app)
        .get('/api/v1/rates-monthly?date=2024-02-29')
        .set('Authorization', `Bearer invalid-token`);

      expect(response.status).toBe(401);
      expect(response.body.error.code).toBe(ERROR_AUTH.TOKEN);
      expect(db.query).toHaveBeenCalledTimes(1);
    });
  });
});
