// Mock database connection
jest.mock('@api/connection');

// Mock jsonwebtoken
jest.mock('jsonwebtoken');

// Mock UUID
const mockUuid = {
  value: '',
  setValue: function (newValue: string) {
    if (!newValue) {
      throw new Error('UUID must be explicitly set in each test');
    }
    this.value = newValue;
  },
  getValue: function () {
    if (!this.value) {
      throw new Error('UUID must be explicitly set before using it');
    }
    return this.value;
  },
};

jest.mock('@api/lib/utils/uuid', () => ({
  __esModule: true,
  default: jest.fn().mockImplementation(() => mockUuid.getValue()),
}));

import { Express } from 'express';
import { Server } from 'http';
import request from 'supertest';
import db from '@api/connection';

import { Auth } from '@api/services';
import { ERROR_AUTH, ERROR_USER } from '@api/lib/constants/error-codes';
import { createTestServer } from '@e2e/support/server-test-utils';

describe('Controllers | User', () => {
  let app: Express;
  let server: Server;
  let authToken: string;
  const testUserId = 'test-user';

  beforeAll(async () => {
    const { app: testApp, server: testServer } = createTestServer();
    app = testApp;
    server = testServer;

    const auth = Auth();
    authToken = auth.sign(testUserId);
  });

  afterAll((done) => {
    if (server) {
      server.close(done);
    } else {
      done();
    }
  });

  beforeEach(() => {
    jest.clearAllMocks();
    mockUuid.value = ''; // Reset UUID for each test to force explicit setting
  });

  afterEach(() => {
    jest.restoreAllMocks();
    // @ts-expect-error - mockClear is jest fn not db query
    db.query.mockClear(); // reset mock call count
  });

  /*
   * Test Plan:
   *
   * PATCH /api/v1/users/:id
   * 1. should update a user successfully
   * 2. should return 401 when user ID doesn't match authenticated user's ID
   * 3. should return 401 when no auth token provided
   * 4. should handle database errors when finding a user
   * 5. should handle database errors when saving a user
   * 6. should handle user not found after update
   */

  describe('PATCH /api/v1/users/:id', () => {
    it('should update a user successfully', async () => {
      const userId = testUserId;
      const originalUser = {
        id: userId,
        email: '<EMAIL>',
        currency: 1,
        dtCreated: 1609459200, // 2021-01-01
        dtLogin: 1609459200,
        licence: 'free',
        jwt: authToken,
        pass: 'hashedPassword',
        otp: '',
        otpExpires: 0,
      };

      const updateData = {
        email: '<EMAIL>',
        currency: 2,
      };

      const updatedUser = {
        ...originalUser,
        ...updateData,
      };

      let findOneCallCount = 0;

      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('SELECT * FROM users WHERE `id`=? AND `jwt`=?')) {
          // Auth check
          cb(null, [{ id: userId, jwt: authToken }]);
        } else if (query.includes('SELECT * FROM users WHERE `id`=?') && !query.includes('jwt')) {
          // First return original user, then return updated user on second call
          findOneCallCount++;
          if (findOneCallCount === 1) {
            cb(null, [originalUser]);
          } else {
            cb(null, [updatedUser]);
          }
        } else if (query.includes('UPDATE users SET')) {
          cb(null, { affectedRows: 1 });
        }
      });

      const response = await request(app).patch(`/api/v1/users/${userId}`).set('Authorization', `Bearer ${authToken}`).send(updateData);

      expect(response.status).toBe(200);
      expect(response.body.data).toEqual({
        currency: 2,
        dtCreated: 1609459200,
        dtLogin: 1609459200,
        email: '<EMAIL>',
        id: userId,
        licence: 'free',
      });

      // Verify database calls
      expect(db.query).toHaveBeenCalledWith(
        expect.stringContaining('UPDATE users SET'),
        expect.arrayContaining([userId]),
        expect.any(Function),
      );
    });

    it("should return 401 when user ID doesn't match authenticated user's ID", async () => {
      const userId = 'different-user';
      const updateData = {
        email: '<EMAIL>',
        currency: 2,
      };

      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('SELECT * FROM users WHERE `id`=? AND `jwt`=?')) {
          cb(null, [{ id: testUserId, jwt: authToken }]);
        }
      });

      const response = await request(app).patch(`/api/v1/users/${userId}`).set('Authorization', `Bearer ${authToken}`).send(updateData);

      expect(response.status).toBe(401);
      expect(response.body.error.code).toBe(ERROR_USER.WRONG_UID);
      // The database should only be queried for auth, not for updates
      expect(db.query).toHaveBeenCalledTimes(1);
    });

    it('should return 401 when no auth token provided', async () => {
      const userId = testUserId;
      const updateData = {
        email: '<EMAIL>',
        currency: 2,
      };

      const response = await request(app).patch(`/api/v1/users/${userId}`).send(updateData);

      expect(response.status).toBe(401);
      expect(response.body.error.code).toBe(ERROR_AUTH.TOKEN);
      expect(db.query).not.toHaveBeenCalled();
    });

    it('should handle database errors when finding a user', async () => {
      const userId = testUserId;
      const updateData = {
        email: '<EMAIL>',
        currency: 2,
      };

      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('SELECT * FROM users WHERE `id`=? AND `jwt`=?')) {
          cb(null, [{ id: userId, jwt: authToken }]);
        } else if (query.includes('SELECT * FROM users WHERE `id`=?') && !query.includes('jwt')) {
          cb(new Error('Database connection failed during fetch'));
        }
      });

      const response = await request(app).patch(`/api/v1/users/${userId}`).set('Authorization', `Bearer ${authToken}`).send(updateData);

      expect(response.status).toBe(500);
      expect(response.body.error.code).toBe(ERROR_USER.FETCH);
      expect(response.body.error.message).toBe('Database connection failed during fetch');
    });

    it('should handle database errors when saving a user', async () => {
      const userId = testUserId;
      const originalUser = {
        id: userId,
        email: '<EMAIL>',
        currency: 1,
        dtCreated: 1609459200,
        dtLogin: 1609459200,
        licence: 'free',
        jwt: authToken,
        pass: 'hashedPassword',
        otp: '',
        otpExpires: 0,
      };

      const updateData = {
        email: '<EMAIL>',
        currency: 2,
      };

      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('SELECT * FROM users WHERE `id`=? AND `jwt`=?')) {
          cb(null, [{ id: userId, jwt: authToken }]);
        } else if (query.includes('SELECT * FROM users WHERE `id`=?') && !query.includes('jwt')) {
          cb(null, [originalUser]);
        } else if (query.includes('UPDATE users SET')) {
          cb(new Error('Database connection failed during save'));
        }
      });

      const response = await request(app).patch(`/api/v1/users/${userId}`).set('Authorization', `Bearer ${authToken}`).send(updateData);

      expect(response.status).toBe(500);
      expect(response.body.error.code).toBe(ERROR_USER.FETCH);
      expect(response.body.error.message).toBe('Database connection failed during save');
    });

    it('should handle user not found after update', async () => {
      const userId = testUserId;
      const originalUser = {
        id: userId,
        email: '<EMAIL>',
        currency: 1,
        dtCreated: 1609459200,
        dtLogin: 1609459200,
        licence: 'free',
        jwt: authToken,
        pass: 'hashedPassword',
        otp: '',
        otpExpires: 0,
      };

      const updateData = {
        email: '<EMAIL>',
        currency: 2,
      };

      let findOneCallCount = 0;

      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('SELECT * FROM users WHERE `id`=? AND `jwt`=?')) {
          cb(null, [{ id: userId, jwt: authToken }]);
        } else if (query.includes('SELECT * FROM users WHERE `id`=?') && !query.includes('jwt')) {
          findOneCallCount++;
          if (findOneCallCount === 1) {
            // First findOne call returns the user
            cb(null, [originalUser]);
          } else {
            // Second findOne call after update returns empty array (user not found)
            cb(null, []);
          }
        } else if (query.includes('UPDATE users SET')) {
          cb(null, { affectedRows: 1 });
        }
      });

      const response = await request(app).patch(`/api/v1/users/${userId}`).set('Authorization', `Bearer ${authToken}`).send(updateData);

      expect(response.status).toBe(500);
      expect(response.body.error.code).toBe(ERROR_USER.UPDATE);
      expect(response.body.error.message).toBe('User update failed');
    });
  });
});
