// Mock database connection
jest.mock('@api/connection');

// Mock jsonwebtoken
jest.mock('jsonwebtoken');

import { Server } from 'http';
import { Application } from 'express';
import request from 'supertest';
import bcrypt from 'bcrypt';

import { ERROR_AUTH } from '@api/lib/constants/error-codes';
import { createTestServer } from '@e2e/support/server-test-utils';
import db from '@api/connection';

describe('Controllers | Auth', () => {
  // Common test utilities
  const makeRequest = (method: string, path: string) => {
    return request(app)[method](`${API_PREFIX}${path}`);
  };

  const expectErrorResponse = (response: any, code: string, status: number) => {
    expect(response.body).toEqual({
      error: {
        code,
        message: expect.any(String),
      },
    });
    expect(response.status).toBe(status);
  };

  // Test setup
  const API_PREFIX = '/api/v1/auth';
  const testUserId = 'test-user';
  const testUserEmail = '<EMAIL>';
  const testUserPass = 'password123';
  const testUserPassHash = bcrypt.hashSync(testUserPass, 12);
  const mockRates = [
    { id: 1, code: 'USD', rate: 1, dtUpdated: Date.now() / 1000 },
    { id: 2, code: 'EUR', rate: 0.85, dtUpdated: Date.now() / 1000 },
  ];

  let app: Application;
  let server: Server;
  let authToken: string;

  beforeAll(() => {
    const { app: testApp, server: testServer } = createTestServer();
    app = testApp;
    server = testServer;
    authToken = 'mock-jwt-token';
  });

  afterAll((done) => {
    if (server) {
      server.close(done);
    } else {
      done();
    }
  });

  beforeEach(() => {
    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.restoreAllMocks();
    // @ts-expect-error - mockClear is jest fn not db query
    db.query.mockClear();
  });

  describe('POST /login', () => {
    it('should return 401 when user not found', async () => {
      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('SELECT * FROM users')) {
          cb(null, []);
        }
      });

      const response = await makeRequest('post', '/login').send({
        email: testUserEmail,
        pass: testUserPass,
      });

      expect(response.status).toBe(401);
      expectErrorResponse(response, ERROR_AUTH.NOT_FOUND, 401);
    });

    it('should return 401 when password is incorrect', async () => {
      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('SELECT * FROM users')) {
          cb(null, [
            {
              id: testUserId,
              email: testUserEmail,
              pass: testUserPassHash,
            },
          ]);
        } else if (query.includes('SELECT * FROM error_codes')) {
          cb(null, [{ message: `Error code: ${ERROR_AUTH.PASS}` }]);
        }
      });

      const response = await makeRequest('post', '/login').send({
        email: testUserEmail,
        pass: 'wrong-password',
      });

      expectErrorResponse(response, ERROR_AUTH.PASS, 401);
    });

    it('should return token and user data when credentials are valid', async () => {
      const mockUser = {
        id: testUserId,
        email: testUserEmail,
        pass: testUserPassHash,
        dtLogin: Math.floor(Date.now() / 1000),
      };

      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('SELECT * FROM users')) {
          cb(null, [mockUser]);
        } else if (query.includes('SELECT * FROM currency_rates')) {
          cb(null, mockRates);
        } else if (query.includes('UPDATE users')) {
          cb(null, { affectedRows: 1 });
        }
      });

      const response = await makeRequest('post', '/login').send({
        email: testUserEmail,
        pass: testUserPass,
      });

      expect(response.status).toBe(200);
      expect(response.body).toEqual({
        token: authToken,
        user: {
          id: testUserId,
          email: testUserEmail,
          dtLogin: expect.any(Number),
          currency: expect.any(Number),
        },
        currencyRates: mockRates,
      });
    });
  });

  describe('POST /register', () => {
    it('should return 422 when user already exists', async () => {
      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('SELECT * FROM users')) {
          cb(null, [
            {
              id: testUserId,
              email: testUserEmail,
            },
          ]);
        } else if (query.includes('SELECT * FROM error_codes')) {
          cb(null, [{ message: `Error code: ${ERROR_AUTH.EXISTS}` }]);
        }
      });

      const response = await makeRequest('post', '/register').send({
        email: testUserEmail,
        pass: testUserPass,
      });

      expectErrorResponse(response, ERROR_AUTH.EXISTS, 422);
      expect(db.query).toHaveBeenCalledWith('SELECT * FROM users WHERE `email`=? LIMIT 1;', [testUserEmail], expect.any(Function));
    });

    it('should create new user and return token when email is unique', async () => {
      const mockUser = {
        id: testUserId,
        email: testUserEmail,
        pass: testUserPassHash,
        dtLogin: Math.floor(Date.now() / 1000),
      };

      let userCheckCount = 0;
      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('SELECT * FROM users')) {
          // First check should return empty (user doesn't exist)
          // Second check after creation should return the user
          userCheckCount++;
          cb(null, userCheckCount === 1 ? [] : [mockUser]);
        } else if (query.includes('INSERT INTO users')) {
          cb(null, { insertId: testUserId });
        } else if (query.includes('SELECT * FROM currency_rates')) {
          cb(null, mockRates);
        } else if (query.includes('UPDATE users')) {
          cb(null, { affectedRows: 1 });
        }
      });

      const response = await makeRequest('post', '/register').send({
        email: testUserEmail,
        pass: testUserPass,
      });

      expect(response.status).toBe(200);
      expect(response.body).toEqual({
        token: authToken,
        user: {
          id: testUserId,
          email: testUserEmail,
          dtLogin: expect.any(Number),
          currency: expect.any(Number),
        },
        currencyRates: mockRates,
      });
    });

    it('should handle database errors during user creation', async () => {
      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('SELECT * FROM users')) {
          // First query to check if user exists - return empty array
          cb(null, []);
        } else if (query.includes('INSERT INTO users')) {
          // Second query to insert user - return error
          cb(new Error('Database error during registration'));
        }
      });

      const response = await makeRequest('post', '/register').send({
        email: testUserEmail,
        pass: testUserPass,
      });

      expect(response.status).toBe(422);
      expect(response.body.error).toBeDefined();
      expect(response.body.error.message).toBe('Database error during registration');
    });
  });

  describe('GET /session', () => {
    it('should return active session when authenticated', async () => {
      const mockUser = {
        id: testUserId,
        email: testUserEmail,
        jwt: authToken,
        dtLogin: Math.floor(Date.now() / 1000),
      };

      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('SELECT * FROM users')) {
          cb(null, [mockUser]);
        } else if (query.includes('SELECT * FROM currency_rates')) {
          cb(null, mockRates);
        } else if (query.includes('UPDATE users')) {
          cb(null, { affectedRows: 1 });
        }
      });

      const response = await makeRequest('get', '/session').set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body).toEqual({
        token: authToken,
        user: {
          id: testUserId,
          email: testUserEmail,
          dtLogin: expect.any(Number),
          currency: expect.any(Number),
        },
        currencyRates: mockRates,
      });
    });

    it('should return 401 when not authenticated', async () => {
      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('SELECT * FROM error_codes')) {
          cb(null, [{ message: `Error code: ${ERROR_AUTH.TOKEN}` }]);
        }
      });

      const response = await makeRequest('get', '/session');
      expectErrorResponse(response, ERROR_AUTH.TOKEN, 401);
    });

    it('should handle database errors in /session endpoint', async () => {
      let queryIndex = 0;

      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        queryIndex++;

        if (queryIndex === 1) {
          // First call (auth check) returns user
          cb(null, [{ id: testUserId, email: testUserEmail, jwt: authToken }]);
        } else if (queryIndex === 2) {
          // Second call (update from user.save) throws error - now it will be caught
          cb(new Error('Database save error'));
        } else {
          // Subsequent calls
          cb(null, []);
        }
      });

      // Make the request
      const response = await makeRequest('get', '/session').set('Authorization', `Bearer ${authToken}`);

      // Assert error response
      expect(response.status).toBe(401);
      expect(response.body.error).toBeDefined();
      expect(response.body.error.message).toBe('Database save error');
    });
  });

  describe('DELETE /logout', () => {
    it('should logout successfully when authenticated', async () => {
      const mockUser = {
        id: testUserId,
        email: testUserEmail,
        jwt: authToken,
      };

      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('SELECT * FROM users')) {
          cb(null, [mockUser]);
        } else if (query.includes('UPDATE users')) {
          cb(null, { affectedRows: 1 });
        }
      });

      const response = await makeRequest('delete', '/logout').set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(204);
      expect(response.body).toEqual({});
      expect(db.query).toHaveBeenCalled();
    });

    it('should return 401 when not authenticated', async () => {
      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('SELECT * FROM error_codes')) {
          cb(null, [{ message: `Error code: ${ERROR_AUTH.TOKEN}` }]);
        }
      });

      const response = await makeRequest('delete', '/logout');
      expectErrorResponse(response, ERROR_AUTH.TOKEN, 401);
    });

    it('should handle errors in the logout endpoint catch block', async () => {
      // First create a flag to track query execution
      let queryCount = 0;

      // Mock database to simulate an error during user update (user.save)
      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        queryCount++;

        if (queryCount === 1) {
          // First query - auth middleware user lookup - return valid user
          cb(null, [{ id: testUserId, email: testUserEmail, jwt: authToken }]);
        } else if (queryCount === 2) {
          // Second query - user.save() - simulate a database error here
          cb(new Error('Database error during user update'));
        } else {
          // Error code lookup
          cb(null, [{ message: 'Database error during user update' }]);
        }
      });

      // Make the request to trigger the error
      const response = await makeRequest('delete', '/logout').set('Authorization', `Bearer ${authToken}`);

      // Verify response matches what we expect from the catch block
      expect(response.status).toBe(401);
      expect(response.body.error).toBeDefined();
      expect(response.body.error.message).toBe('Database error during user update');

      // Make sure database was queried
      expect(db.query).toHaveBeenCalled();
      expect(queryCount).toBeGreaterThan(1);
    });
  });

  describe('Error Handling', () => {
    it('should handle database errors gracefully', async () => {
      const dbError = new Error('Database connection failed') as Error & { code: string };
      dbError.code = 'DB_ERROR';

      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        cb(dbError);
      });

      const response = await makeRequest('post', '/login').send({
        email: testUserEmail,
        pass: testUserPass,
      });

      expect(response.status).toBe(401);
      expect(response.body.error.message).toBe('Database connection failed');
    });

    it('should handle errors during user lookup in login', async () => {
      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('SELECT * FROM users')) {
          // Simulate database error during user lookup
          cb(new Error('User lookup failed'));
        }
      });

      const response = await makeRequest('post', '/login').send({
        email: testUserEmail,
        pass: testUserPass,
      });

      expect(response.status).toBe(401);
      expect(response.body.error.message).toBe('User lookup failed');
    });
  });
});
