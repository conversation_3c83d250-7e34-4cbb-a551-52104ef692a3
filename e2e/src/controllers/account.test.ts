// Mock database connection
jest.mock('@api/connection');

// Mock jsonwebtoken
jest.mock('jsonwebtoken');

import { Express } from 'express';
import { Server } from 'http';
import request from 'supertest';
import db from '@api/connection';

import { Auth } from '@api/services';
import { ERROR_AUTH, ERROR_ACCOUNT } from '@api/lib/constants/error-codes';
import { ACCOUNT_TYPES, ACCOUNT_TAX_TREATMENTS } from '@api/lib/constants/accounts';
import { createTestServer } from '@e2e/support/server-test-utils';

describe('Controllers | Account', () => {
  let app: Express;
  let server: Server;
  let authToken: string;
  const testUserId = 'test-user';

  beforeAll(async () => {
    const { app: testApp, server: testServer } = createTestServer();
    app = testApp;
    server = testServer;

    const auth = Auth();
    authToken = auth.sign(testUserId);
  });

  afterAll((done) => {
    if (server) {
      server.close(done);
    } else {
      done();
    }
  });

  beforeEach(() => {
    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.restoreAllMocks();
    // @ts-expect-error - mockClear is jest fn not db query
    db.query.mockClear(); // reset mock call count
  });

  describe('GET /api/v1/accounts', () => {
    it('should handle database errors', async () => {
      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('SELECT * FROM users')) {
          cb(null, [{ id: testUserId, jwt: authToken }]);
        } else if (query.includes('SELECT * FROM accounts')) {
          cb(new Error('Database connection failed'));
        }
      });

      const response = await request(app).get('/api/v1/accounts').set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(500);
      expect(response.body.error.code).toBe(ERROR_ACCOUNT.FETCH);
      expect(response.body.error.message).toBe('Database connection failed');
      expect(db.query).toHaveBeenCalledTimes(2);
      expect(db.query).toHaveBeenNthCalledWith(
        1,
        'SELECT * FROM users WHERE `id`=? AND `jwt`=? LIMIT 1;',
        [testUserId, authToken],
        expect.any(Function),
      );
    });

    it('should return empty list when no accounts exist', async () => {
      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('users')) {
          // Return mock user for auth check
          cb(null, [{ id: testUserId, jwt: authToken }]);
        } else {
          // Return empty accounts list
          cb(null, []);
        }
      });

      const response = await request(app).get('/api/v1/accounts').set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.data).toEqual([]);
      expect(db.query).toHaveBeenCalled();
    });

    it('should return 401 when no auth token provided', async () => {
      const response = await request(app).get('/api/v1/accounts');

      expect(response.status).toBe(401);
      expect(response.body.error.code).toBe(ERROR_AUTH.TOKEN);
      expect(db.query).not.toHaveBeenCalled();
    });

    it('should return list of accounts for authenticated user', async () => {
      const mockAccounts = [
        {
          id: 1,
          uid: testUserId,
          name: 'Investment Account',
          type: ACCOUNT_TYPES.SELF_DIRECTED,
          taxTreatment: ACCOUNT_TAX_TREATMENTS.TAXABLE,
          withholdingTax: 15,
          description: 'Main investment account',
        },
        {
          id: 2,
          uid: testUserId,
          name: 'Retirement Account',
          type: ACCOUNT_TYPES.MANAGED,
          taxTreatment: ACCOUNT_TAX_TREATMENTS.TAX_FREE,
          withholdingTax: 0,
          description: 'Tax-free retirement savings',
        },
      ];

      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('users')) {
          // Return mock user for auth check
          cb(null, [{ id: testUserId, jwt: authToken }]);
        } else {
          // Return mock accounts list
          cb(null, mockAccounts);
        }
      });

      const response = await request(app).get('/api/v1/accounts').set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.data).toEqual(mockAccounts);
      expect(db.query).toHaveBeenCalled();
    });

    it('should pass sort parameter to findAll when provided', async () => {
      const mockAccounts = [
        {
          id: 1,
          uid: testUserId,
          name: 'Apple Investment Account',
          type: ACCOUNT_TYPES.SELF_DIRECTED,
          taxTreatment: ACCOUNT_TAX_TREATMENTS.TAXABLE,
          withholdingTax: 15,
        },
        {
          id: 2,
          uid: testUserId,
          name: 'Banana Savings Account',
          type: ACCOUNT_TYPES.MANAGED,
          taxTreatment: ACCOUNT_TAX_TREATMENTS.TAX_FREE,
          withholdingTax: 0,
        },
      ];

      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('users')) {
          // Return mock user for auth check
          cb(null, [{ id: testUserId, jwt: authToken }]);
        } else {
          // Return mock accounts list sorted by name
          const sortedAccounts = [...mockAccounts].sort((a, b) => {
            if (query.includes('DESC')) {
              return b.name.localeCompare(a.name);
            }
            return a.name.localeCompare(b.name);
          });
          cb(null, sortedAccounts);
        }
      });

      // Test ascending sort
      const responseAsc = await request(app).get('/api/v1/accounts').query({ sort: 'name' }).set('Authorization', `Bearer ${authToken}`);

      expect(responseAsc.status).toBe(200);
      expect(responseAsc.body.data).toEqual([...mockAccounts].sort((a, b) => a.name.localeCompare(b.name)));
      expect(db.query).toHaveBeenCalled();

      // Test descending sort
      const responseDesc = await request(app).get('/api/v1/accounts').query({ sort: '-name' }).set('Authorization', `Bearer ${authToken}`);

      expect(responseDesc.status).toBe(200);
      expect(responseDesc.body.data).toEqual([...mockAccounts].sort((a, b) => b.name.localeCompare(a.name)));
      expect(db.query).toHaveBeenCalled();
    });
  });

  describe('POST /api/v1/accounts', () => {
    it('should replace with single account when replace=true', async () => {
      const accountData = {
        name: 'New Investment Account',
        type: ACCOUNT_TYPES.SELF_DIRECTED,
        taxTreatment: ACCOUNT_TAX_TREATMENTS.TAXABLE,
        withholdingTax: 15,
        description: 'Replacement account',
      };

      const createdAccount = {
        ...accountData,
        id: 1,
        uid: testUserId,
      };

      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('SELECT * FROM users')) {
          cb(null, [{ id: testUserId, jwt: authToken }]);
        } else if (query.includes('DELETE')) {
          cb(null, { affectedRows: 2 }); // Simulating deletion of existing accounts
        } else if (query.includes('INSERT')) {
          cb(null, { insertId: 1, affectedRows: 1 });
        } else if (query.includes('SELECT * FROM accounts')) {
          cb(null, [createdAccount]);
        }
      });

      const response = await request(app)
        .post('/api/v1/accounts')
        .query({ replace: 'true' })
        .set('Authorization', `Bearer ${authToken}`)
        .send(accountData);

      expect(response.status).toBe(201);
      expect(response.body.data).toEqual([createdAccount]);
      expect(db.query).toHaveBeenCalled();
    });

    it('should create a single account successfully', async () => {
      const accountData = {
        name: 'Test Investment Account',
        type: ACCOUNT_TYPES.SELF_DIRECTED,
        taxTreatment: ACCOUNT_TAX_TREATMENTS.TAXABLE,
        withholdingTax: 15,
        description: 'A test account',
      };

      const createdAccount = {
        ...accountData,
        id: 1,
        uid: testUserId,
      };

      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('users')) {
          // Return mock user for auth check
          cb(null, [{ id: testUserId, jwt: authToken }]);
        } else if (query.includes('INSERT')) {
          // Return the created account
          cb(null, { insertId: 1, affectedRows: 1 });
        }
      });

      const response = await request(app).post('/api/v1/accounts').set('Authorization', `Bearer ${authToken}`).send(accountData);

      expect(response.status).toBe(201);
      expect(response.body.data).toEqual(createdAccount);
      expect(db.query).toHaveBeenCalled();
    });

    it('should create multiple accounts successfully', async () => {
      const accountsData = [
        {
          name: 'First Investment Account',
          type: ACCOUNT_TYPES.SELF_DIRECTED,
          taxTreatment: ACCOUNT_TAX_TREATMENTS.TAXABLE,
          withholdingTax: 15,
          description: 'First test account',
        },
        {
          name: 'Second Savings Account',
          type: ACCOUNT_TYPES.MANAGED,
          taxTreatment: ACCOUNT_TAX_TREATMENTS.TAX_FREE,
          withholdingTax: 0,
          description: 'Second test account',
        },
      ];

      const expectedAccounts = accountsData.map((data, index) => ({
        ...data,
        id: index + 1,
        uid: testUserId,
      }));

      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('SELECT * FROM users')) {
          cb(null, [{ id: testUserId, jwt: authToken }]);
        } else if (query.includes('INSERT')) {
          cb(null, { insertId: 1, affectedRows: 1 });
        } else if (query.includes('SELECT * FROM accounts')) {
          cb(null, expectedAccounts);
        }
      });

      const response = await request(app).post('/api/v1/accounts').set('Authorization', `Bearer ${authToken}`).send(accountsData);

      expect(response.status).toBe(201);
      expect(response.body.data).toEqual(expectedAccounts);
      expect(db.query).toHaveBeenCalled();
    });

    it('should replace all accounts when replace=true query parameter is provided', async () => {
      const accountsData = [
        {
          name: 'Main Investment Account',
          type: ACCOUNT_TYPES.SELF_DIRECTED,
          taxTreatment: ACCOUNT_TAX_TREATMENTS.TAXABLE,
          withholdingTax: 15,
          description: 'Primary investment account',
        },
        {
          name: 'Retirement Savings',
          type: ACCOUNT_TYPES.MANAGED,
          taxTreatment: ACCOUNT_TAX_TREATMENTS.TAX_FREE,
          withholdingTax: 0,
          description: 'Tax-free retirement account',
        },
      ];

      const createdAccounts = accountsData.map((data, index) => ({
        ...data,
        id: index + 1,
        uid: testUserId,
      }));

      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('users')) {
          cb(null, [{ id: testUserId, jwt: authToken }]);
        } else if (query.includes('DELETE')) {
          cb(null, { affectedRows: 2 });
        } else if (query.includes('INSERT')) {
          cb(null, { insertId: 1, affectedRows: 1 });
        } else if (query.includes('SELECT')) {
          cb(null, createdAccounts);
        }
      });

      const response = await request(app)
        .post('/api/v1/accounts')
        .query({ replace: 'true' })
        .set('Authorization', `Bearer ${authToken}`)
        .send(accountsData);

      expect(response.status).toBe(201);
      expect(response.body.data).toEqual(createdAccounts);
      expect(db.query).toHaveBeenCalled();
    });

    it('should return 401 when no auth token provided', async () => {
      const accountData = {
        name: 'Test Account',
        type: ACCOUNT_TYPES.SELF_DIRECTED,
        taxTreatment: ACCOUNT_TAX_TREATMENTS.TAXABLE,
      };

      const response = await request(app).post('/api/v1/accounts').send(accountData);

      expect(response.status).toBe(401);
      expect(response.body.error.code).toBe(ERROR_AUTH.TOKEN);
      expect(db.query).not.toHaveBeenCalled();
    });

    it('should handle validation/database errors', async () => {
      const accountData = {
        name: 'Test Account',
        type: ACCOUNT_TYPES.SELF_DIRECTED,
        taxTreatment: ACCOUNT_TAX_TREATMENTS.TAXABLE,
      };

      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('users')) {
          // Return mock user for auth check
          cb(null, [{ id: testUserId, jwt: authToken }]);
        } else if (query.includes('INSERT')) {
          // Simulate database error
          cb(new Error('Database connection failed'));
        }
      });

      const response = await request(app).post('/api/v1/accounts').set('Authorization', `Bearer ${authToken}`).send(accountData);

      expect(response.status).toBe(500);
      expect(response.body.error).toEqual({
        code: ERROR_ACCOUNT.CREATE,
        message: 'Database connection failed',
      });
      expect(db.query).toHaveBeenCalled();
    });

    it('should handle invalid account data', async () => {
      const invalidData = {
        type: ACCOUNT_TYPES.SELF_DIRECTED,
        withholdingTax: 15,
        // missing taxTreatment
      };

      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('SELECT * FROM users')) {
          cb(null, [{ id: testUserId, jwt: authToken }]);
        }
      });

      const response = await request(app).post('/api/v1/accounts').set('Authorization', `Bearer ${authToken}`).send(invalidData);

      expect(response.status).toBe(400);
      expect(response.body.error.code).toBe(ERROR_ACCOUNT.PATCH_TYPE_ALL);
      expect(response.body.error.message).toBe('Invalid account data. Account must have name property.');
      expect(db.query).toHaveBeenCalledTimes(1);
      expect(db.query).toHaveBeenCalledWith(
        'SELECT * FROM users WHERE `id`=? AND `jwt`=? LIMIT 1;',
        [testUserId, authToken],
        expect.any(Function),
      );
    });

    it('should handle invalid accounts in array', async () => {
      const invalidData = [
        {
          name: 'Valid Account',
          type: ACCOUNT_TYPES.SELF_DIRECTED,
          taxTreatment: ACCOUNT_TAX_TREATMENTS.TAXABLE,
        },
        {
          type: ACCOUNT_TYPES.MANAGED,
          // missing taxTreatment
        },
      ];

      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('SELECT * FROM users')) {
          cb(null, [{ id: testUserId, jwt: authToken }]);
        }
      });

      const response = await request(app).post('/api/v1/accounts').set('Authorization', `Bearer ${authToken}`).send(invalidData);

      expect(response.status).toBe(400);
      expect(response.body.error.code).toBe(ERROR_ACCOUNT.PATCH_TYPE_ALL);
      expect(response.body.error.message).toBe('Invalid account data. Each account must have name property.');
      expect(db.query).toHaveBeenCalledTimes(1);
      expect(db.query).toHaveBeenCalledWith(
        'SELECT * FROM users WHERE `id`=? AND `jwt`=? LIMIT 1;',
        [testUserId, authToken],
        expect.any(Function),
      );
    });
  });

  describe('PATCH /api/v1/accounts', () => {
    it('should update multiple accounts successfully', async () => {
      const accountsData = [
        {
          id: 1,
          name: 'Updated First Account',
          description: 'Updated first description',
          withholdingTax: 20,
          taxTreatment: ACCOUNT_TAX_TREATMENTS.TAXABLE,
        },
        {
          id: 2,
          name: 'Updated Second Account',
          description: 'Updated second description',
          withholdingTax: 25,
          taxTreatment: ACCOUNT_TAX_TREATMENTS.TAX_FREE,
        },
      ];

      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('users')) {
          cb(null, [{ id: testUserId, jwt: authToken }]);
        } else if (query.includes('UPDATE')) {
          cb(null, { affectedRows: 1 });
        } else if (query.includes('SELECT')) {
          cb(
            null,
            accountsData.map((account) => ({
              ...account,
              type: ACCOUNT_TYPES.SELF_DIRECTED,
              uid: testUserId,
            })),
          );
        }
      });

      const response = await request(app).patch('/api/v1/accounts').set('Authorization', `Bearer ${authToken}`).send(accountsData);

      expect(response.status).toBe(200);
      expect(response.body.data).toHaveLength(2);
      expect(response.body.data[0]).toMatchObject(accountsData[0]);
      expect(response.body.data[1]).toMatchObject(accountsData[1]);
      expect(db.query).toHaveBeenCalled();
    });

    it('should update single account through bulk endpoint', async () => {
      const accountData = {
        id: 1,
        name: 'Updated Single Account',
        description: 'Updated description',
        withholdingTax: 15,
        taxTreatment: ACCOUNT_TAX_TREATMENTS.TAXABLE,
      };

      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('users')) {
          cb(null, [{ id: testUserId, jwt: authToken }]);
        } else if (query.includes('UPDATE')) {
          cb(null, { affectedRows: 1 });
        } else if (query.includes('SELECT')) {
          cb(null, [
            {
              ...accountData,
              type: ACCOUNT_TYPES.SELF_DIRECTED,
              uid: testUserId,
            },
          ]);
        }
      });

      const response = await request(app).patch('/api/v1/accounts').set('Authorization', `Bearer ${authToken}`).send([accountData]);

      expect(response.status).toBe(200);
      expect(response.body.data).toHaveLength(1);
      expect(response.body.data[0]).toMatchObject(accountData);
      expect(db.query).toHaveBeenCalled();
    });

    it('should update a single account when non-array object is sent', async () => {
      const accountData = {
        id: 1,
        name: 'Updated Account Name',
        description: 'Updated description',
        withholdingTax: 10,
        taxTreatment: ACCOUNT_TAX_TREATMENTS.TAXABLE,
      };

      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('users')) {
          cb(null, [{ id: testUserId, jwt: authToken }]);
        } else if (query.includes('UPDATE')) {
          cb(null, { affectedRows: 1 });
        }
      });

      const response = await request(app).patch('/api/v1/accounts').set('Authorization', `Bearer ${authToken}`).send(accountData);

      // This specific branch returns 204 with empty JSON
      expect(response.status).toBe(204);
      expect(response.body).toEqual({});
      expect(db.query).toHaveBeenCalled();

      // Verify the query contained the account ID and user ID
      expect(db.query).toHaveBeenCalledWith(
        expect.stringContaining('UPDATE accounts'),
        expect.arrayContaining([accountData.id, testUserId]),
        expect.any(Function),
      );
    });

    it('should return 401 when no auth token provided', async () => {
      const accountData = [
        {
          id: 1,
          name: 'Updated Account',
          taxTreatment: ACCOUNT_TAX_TREATMENTS.TAXABLE,
        },
      ];

      const response = await request(app).patch('/api/v1/accounts').send(accountData);

      expect(response.status).toBe(401);
      expect(response.body.error.code).toBe(ERROR_AUTH.TOKEN);
      expect(db.query).not.toHaveBeenCalled();
    });

    it('should handle validation/database errors', async () => {
      const accountData = [
        {
          id: 1,
          name: 'Test Account',
          taxTreatment: ACCOUNT_TAX_TREATMENTS.TAXABLE,
        },
      ];

      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('users')) {
          cb(null, [{ id: testUserId, jwt: authToken }]);
        } else if (query.includes('SELECT')) {
          cb(null, [
            {
              ...accountData[0],
              uid: testUserId,
              type: ACCOUNT_TYPES.SELF_DIRECTED,
            },
          ]);
        } else if (query.includes('UPDATE')) {
          cb(new Error('Database connection failed')); // Pass error as first argument
        }
      });

      const response = await request(app).patch('/api/v1/accounts').set('Authorization', `Bearer ${authToken}`).send(accountData);

      expect(response.status).toBe(500);
      expect(response.body.error).toEqual({
        code: ERROR_ACCOUNT.PATCH_ALL,
        message: 'Database connection failed',
      });
      expect(db.query).toHaveBeenCalled();
    });

    it('should update single account', async () => {
      const accountId = 1;
      const updateData = {
        name: 'Updated Account Name',
        description: 'Updated description',
        withholdingTax: 20,
        taxTreatment: ACCOUNT_TAX_TREATMENTS.TAXABLE,
      };

      const existingAccount = {
        id: accountId,
        uid: testUserId,
        name: 'Old Account Name',
        type: ACCOUNT_TYPES.SELF_DIRECTED,
        taxTreatment: ACCOUNT_TAX_TREATMENTS.TAXABLE,
        withholdingTax: 15,
      };

      const updatedAccount = {
        ...existingAccount,
        ...updateData,
      };

      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('users')) {
          cb(null, [{ id: testUserId, jwt: authToken }]);
        } else if (query.includes('SELECT')) {
          cb(null, [existingAccount]);
        } else if (query.includes('UPDATE')) {
          cb(null, [updatedAccount]);
        }
      });

      const response = await request(app)
        .patch(`/api/v1/accounts/${accountId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send(updateData);

      expect(response.status).toBe(200);
      expect(response.body.data).toMatchObject(updatedAccount);
      expect(db.query).toHaveBeenCalled();
    });

    it('should handle error when updating single account', async () => {
      const accountData = {
        id: 1,
        name: 'Updated Account',
        taxTreatment: ACCOUNT_TAX_TREATMENTS.TAXABLE,
      };

      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('SELECT * FROM users')) {
          cb(null, [{ id: testUserId, jwt: authToken }]);
        } else if (query.includes('UPDATE accounts')) {
          cb(new Error('Database connection failed'));
        }
      });

      const response = await request(app).patch('/api/v1/accounts').set('Authorization', `Bearer ${authToken}`).send(accountData);

      expect(response.status).toBe(500);
      expect(response.body.error.code).toBe(ERROR_ACCOUNT.PATCH);
      expect(response.body.error.message).toBe('Database connection failed');
      expect(db.query).toHaveBeenCalled();
    });

    it('should handle invalid input type', async () => {
      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('SELECT * FROM users')) {
          cb(null, [{ id: testUserId, jwt: authToken }]);
        }
      });

      const response = await request(app).patch('/api/v1/accounts').set('Authorization', `Bearer ${authToken}`).send({});

      expect(response.status).toBe(400);
      expect(response.body.error.code).toBe(ERROR_ACCOUNT.PATCH_TYPE_ALL);
      expect(response.body.error.message).toBe('Invalid account data. Account must have name property.');
      expect(db.query).toHaveBeenCalledTimes(1);
      expect(db.query).toHaveBeenCalledWith(
        'SELECT * FROM users WHERE `id`=? AND `jwt`=? LIMIT 1;',
        [testUserId, authToken],
        expect.any(Function),
      );
    });

    it('should handle invalid accounts in array', async () => {
      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('SELECT * FROM users')) {
          cb(null, [{ id: testUserId, jwt: authToken }]);
        }
      });

      const invalidAccountsData = [
        {
          id: 1,
          name: 'Valid Account',
          taxTreatment: ACCOUNT_TAX_TREATMENTS.TAXABLE,
        },
        {
          id: 2,
          // missing taxTreatment
        },
      ];

      const response = await request(app).patch('/api/v1/accounts').set('Authorization', `Bearer ${authToken}`).send(invalidAccountsData);

      expect(response.status).toBe(400);
      expect(response.body.error.code).toBe(ERROR_ACCOUNT.PATCH_TYPE_ALL);
      expect(response.body.error.message).toBe('Invalid account data. Each account must have name property.');
      expect(db.query).toHaveBeenCalledTimes(1);
      expect(db.query).toHaveBeenCalledWith(
        'SELECT * FROM users WHERE `id`=? AND `jwt`=? LIMIT 1;',
        [testUserId, authToken],
        expect.any(Function),
      );
    });
  });

  describe('PATCH /api/v1/accounts/:id', () => {
    it('should handle invalid account data', async () => {
      const accountId = 1;
      const invalidData = {
        name: 'Updated Account',
        withholdingTax: 20,
        // missing taxTreatment
      };

      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('SELECT * FROM users')) {
          cb(null, [{ id: testUserId, jwt: authToken }]);
        }
      });

      const response = await request(app)
        .patch(`/api/v1/accounts/${accountId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send(invalidData);

      expect(response.status).toBe(400);
      expect(response.body.error.code).toBe(ERROR_ACCOUNT.PATCH_TYPE_ALL);
      expect(response.body.error.message).toBe('Invalid account data. Account must have name property.');
      expect(db.query).toHaveBeenCalledTimes(1);
      expect(db.query).toHaveBeenCalledWith(
        'SELECT * FROM users WHERE `id`=? AND `jwt`=? LIMIT 1;',
        [testUserId, authToken],
        expect.any(Function),
      );
    });

    it('should update specific account fields successfully', async () => {
      const accountId = 1;
      const updateData = {
        name: 'Updated Account Name',
        description: 'Updated description',
        withholdingTax: 20,
        taxTreatment: ACCOUNT_TAX_TREATMENTS.TAXABLE,
      };

      const existingAccount = {
        id: accountId,
        uid: testUserId,
        name: 'Old Account Name',
        type: ACCOUNT_TYPES.SELF_DIRECTED,
        taxTreatment: ACCOUNT_TAX_TREATMENTS.TAXABLE,
        withholdingTax: 15,
      };

      const updatedAccount = {
        ...existingAccount,
        ...updateData,
      };

      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('users')) {
          cb(null, [{ id: testUserId, jwt: authToken }]);
        } else if (query.includes('SELECT')) {
          cb(null, [existingAccount]);
        } else if (query.includes('UPDATE')) {
          cb(null, [updatedAccount]);
        }
      });

      const response = await request(app)
        .patch(`/api/v1/accounts/${accountId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send(updateData);

      expect(response.status).toBe(200);
      expect(response.body.data).toMatchObject(updatedAccount);
      expect(db.query).toHaveBeenCalled();
    });

    it('should return 404 for non-existent account', async () => {
      const accountId = 999;
      const updateData = {
        name: 'Updated Account',
        taxTreatment: ACCOUNT_TAX_TREATMENTS.TAXABLE,
      };

      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('users')) {
          cb(null, [{ id: testUserId, jwt: authToken }]);
        } else if (query.includes('SELECT')) {
          // Return empty result to simulate non-existent account
          cb(null, []);
        }
      });

      const response = await request(app)
        .patch(`/api/v1/accounts/${accountId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send(updateData);

      expect(response.status).toBe(404);
      expect(response.body.error.code).toBe(ERROR_ACCOUNT.NOT_FOUND);
      expect(db.query).toHaveBeenCalled();
    });

    it('should return 401 when no auth token provided', async () => {
      const accountId = 1;
      const updateData = {
        name: 'Updated Account',
        taxTreatment: ACCOUNT_TAX_TREATMENTS.TAXABLE,
      };

      const response = await request(app).patch(`/api/v1/accounts/${accountId}`).send(updateData);

      expect(response.status).toBe(401);
      expect(response.body.error.code).toBe(ERROR_AUTH.TOKEN);
      expect(db.query).not.toHaveBeenCalled();
    });

    it('should handle validation/database errors', async () => {
      const accountId = 1;
      const updateData = {
        name: 'Updated Account',
        taxTreatment: ACCOUNT_TAX_TREATMENTS.TAXABLE,
      };

      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('users')) {
          cb(null, [{ id: testUserId, jwt: authToken }]);
        } else if (query.includes('SELECT')) {
          cb(null, [
            {
              id: accountId,
              uid: testUserId,
              name: 'Old Account',
              taxTreatment: ACCOUNT_TAX_TREATMENTS.TAXABLE,
            },
          ]);
        } else if (query.includes('UPDATE')) {
          cb(new Error('Database connection failed'));
        }
      });

      const response = await request(app)
        .patch(`/api/v1/accounts/${accountId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send(updateData);

      expect(response.status).toBe(500);
      expect(response.body.error).toEqual({
        code: ERROR_ACCOUNT.UPDATE,
        message: 'Database connection failed',
      });
      expect(db.query).toHaveBeenCalled();
    });
  });

  describe('DELETE /api/v1/accounts/:id', () => {
    it('should delete an account successfully', async () => {
      const accountId = 1;

      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('users')) {
          cb(null, [{ id: testUserId, jwt: authToken }]);
        } else if (query.includes('DELETE')) {
          cb(null, { affectedRows: 1 });
        } else if (query.includes('SELECT')) {
          cb(null, [
            {
              id: accountId,
              uid: testUserId,
              name: 'Test Account',
              type: ACCOUNT_TYPES.SELF_DIRECTED,
              taxTreatment: ACCOUNT_TAX_TREATMENTS.TAXABLE,
            },
          ]);
        }
      });

      const response = await request(app).delete(`/api/v1/accounts/${accountId}`).set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(204);
      expect(response.body).toEqual({});
      expect(db.query).toHaveBeenCalled();
    });

    it('should return 404 for non-existent account', async () => {
      const accountId = 999;

      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('users')) {
          cb(null, [{ id: testUserId, jwt: authToken }]);
        } else if (query.includes('SELECT')) {
          cb(null, []);
        } else if (query.includes('DELETE')) {
          cb(null, { affectedRows: 0 });
        }
      });

      const response = await request(app).delete(`/api/v1/accounts/${accountId}`).set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(404);
      expect(response.body.error.code).toBe(ERROR_ACCOUNT.NOT_FOUND);
      expect(db.query).toHaveBeenCalled();
    });

    it('should return 401 when no auth token provided', async () => {
      const accountId = 1;

      const response = await request(app).delete(`/api/v1/accounts/${accountId}`);

      expect(response.status).toBe(401);
      expect(response.body.error.code).toBe(ERROR_AUTH.TOKEN);
      expect(db.query).not.toHaveBeenCalled();
    });

    it('should handle database errors', async () => {
      const accountId = 1;

      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('users')) {
          cb(null, [{ id: testUserId, jwt: authToken }]);
        } else if (query.includes('SELECT')) {
          cb(null, [
            {
              id: accountId,
              uid: testUserId,
            },
          ]);
        } else if (query.includes('DELETE')) {
          cb(new Error('Database connection failed'));
        }
      });

      const response = await request(app).delete(`/api/v1/accounts/${accountId}`).set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(500);
      expect(response.body.error).toEqual({
        code: ERROR_ACCOUNT.DELETE,
        message: 'Database connection failed',
      });
      expect(db.query).toHaveBeenCalled();
    });
  });
});
