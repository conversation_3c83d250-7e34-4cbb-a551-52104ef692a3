// Mock database connection
jest.mock('@api/connection');

// Mock jsonwebtoken
jest.mock('jsonwebtoken');

import { Express } from 'express';
import { Server } from 'http';
import request from 'supertest';
import db from '@api/connection';

import { Auth } from '@api/services';
import { ERROR_AUTH, ERROR_RATE } from '@api/lib/constants/error-codes';
import { createTestServer } from '@e2e/support/server-test-utils';

describe('Controllers | Rate', () => {
  let app: Express;
  let server: Server;
  let authToken: string;
  const testUserId = 'test-user';

  beforeAll(async () => {
    const { app: testApp, server: testServer } = createTestServer();
    app = testApp;
    server = testServer;

    const auth = Auth();
    authToken = auth.sign(testUserId);
  });

  afterAll((done) => {
    if (server) {
      server.close(done);
    } else {
      done();
    }
  });

  beforeEach(() => {
    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.restoreAllMocks();
    // @ts-expect-error - mockClear is jest fn not db query
    db.query.mockClear(); // reset mock call count
  });

  describe('GET /api/v1/rates', () => {
    it('should return a list of currency rates for authenticated user', async () => {
      const mockRates = [
        { id: 1, code: 'USD', rate: 1, dtUpdated: Math.floor(Date.now() / 1000) },
        { id: 2, code: 'EUR', rate: 0.85, dtUpdated: Math.floor(Date.now() / 1000) },
        { id: 3, code: 'GBP', rate: 0.75, dtUpdated: Math.floor(Date.now() / 1000) },
      ];

      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('SELECT * FROM users')) {
          cb(null, [{ id: testUserId, jwt: authToken }]);
        } else if (query.includes('SELECT * FROM currency_rates')) {
          cb(null, mockRates);
        }
      });

      const response = await request(app).get('/api/v1/rates').set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.data).toEqual(mockRates);
      expect(db.query).toHaveBeenCalledTimes(2);
      expect(db.query).toHaveBeenNthCalledWith(
        1,
        'SELECT * FROM users WHERE `id`=? AND `jwt`=? LIMIT 1;',
        [testUserId, authToken],
        expect.any(Function),
      );
    });

    it('should return empty list when no rates exist', async () => {
      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('SELECT * FROM users')) {
          cb(null, [{ id: testUserId, jwt: authToken }]);
        } else if (query.includes('SELECT * FROM currency_rates')) {
          cb(null, []);
        }
      });

      const response = await request(app).get('/api/v1/rates').set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.data).toEqual([]);
      expect(db.query).toHaveBeenCalled();
    });

    it('should handle database errors', async () => {
      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('SELECT * FROM users')) {
          cb(null, [{ id: testUserId, jwt: authToken }]);
        } else if (query.includes('SELECT * FROM currency_rates')) {
          cb(new Error('Database connection failed'));
        }
      });

      const response = await request(app).get('/api/v1/rates').set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(500);
      expect(response.body.error.code).toBe(ERROR_RATE.FETCH);
      expect(response.body.error.message).toBe('Database connection failed');
      expect(db.query).toHaveBeenCalledTimes(2);
      expect(db.query).toHaveBeenNthCalledWith(
        1,
        'SELECT * FROM users WHERE `id`=? AND `jwt`=? LIMIT 1;',
        [testUserId, authToken],
        expect.any(Function),
      );
    });

    it('should return 401 when no auth token provided', async () => {
      const response = await request(app).get('/api/v1/rates');

      expect(response.status).toBe(401);
      expect(response.body.error.code).toBe(ERROR_AUTH.TOKEN);
      expect(db.query).not.toHaveBeenCalled();
    });

    it('should return 401 when invalid auth token provided', async () => {
      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('SELECT * FROM users')) {
          // Return empty array to simulate invalid token
          cb(null, []);
        }
      });

      const response = await request(app).get('/api/v1/rates').set('Authorization', `Bearer invalid-token`);

      expect(response.status).toBe(401);
      expect(response.body.error.code).toBe(ERROR_AUTH.TOKEN);
      expect(db.query).toHaveBeenCalledTimes(1);
    });
  });
});
