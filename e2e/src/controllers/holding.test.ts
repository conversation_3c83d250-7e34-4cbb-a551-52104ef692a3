// Mock database connection
jest.mock('@api/connection');

// Mock jsonwebtoken
jest.mock('jsonwebtoken');

// Mock UUID
const mockUuid = {
  value: '',
  setValue: function (newValue: string) {
    if (!newValue) {
      throw new Error('UUID must be explicitly set in each test');
    }
    this.value = newValue;
  },
  getValue: function () {
    if (!this.value) {
      throw new Error('UUID must be explicitly set before using it');
    }
    return this.value;
  },
};

jest.mock('@api/lib/utils/uuid', () => ({
  __esModule: true,
  default: jest.fn().mockImplementation(() => mockUuid.getValue()),
}));

import { Express } from 'express';
import { Server } from 'http';
import request from 'supertest';
import db from '@api/connection';

import { Auth } from '@api/services';
import { ERROR_AUTH, ERROR_HOLDING } from '@api/lib/constants/error-codes';
import { createTestServer } from '@e2e/support/server-test-utils';

describe('Controllers | Holding', () => {
  let app: Express;
  let server: Server;
  let authToken: string;
  const testUserId = 'test-user';

  beforeAll(async () => {
    const { app: testApp, server: testServer } = createTestServer();
    app = testApp;
    server = testServer;

    const auth = Auth();
    authToken = auth.sign(testUserId);
  });

  afterAll((done) => {
    if (server) {
      server.close(done);
    } else {
      done();
    }
  });

  beforeEach(() => {
    jest.clearAllMocks();
    mockUuid.value = ''; // Reset UUID for each test to force explicit setting
  });

  afterEach(() => {
    jest.restoreAllMocks();
    // @ts-expect-error - mockClear is jest fn not db query
    db.query.mockClear(); // reset mock call count
  });

  describe('GET /api/v1/holdings', () => {
    it('should handle database errors', async () => {
      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('SELECT * FROM users')) {
          cb(null, [{ id: testUserId, jwt: authToken }]);
        } else if (query.includes('SELECT * FROM holdings')) {
          cb(new Error('Database connection failed'));
        }
      });

      const response = await request(app).get('/api/v1/holdings').set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(500);
      expect(response.body.error.code).toBe(ERROR_HOLDING.FETCH);
      expect(response.body.error.message).toBe('Database connection failed');
      expect(db.query).toHaveBeenCalledTimes(2);
      expect(db.query).toHaveBeenNthCalledWith(
        1,
        'SELECT * FROM users WHERE `id`=? AND `jwt`=? LIMIT 1;',
        [testUserId, authToken],
        expect.any(Function),
      );
    });

    it('should return empty list when no holdings exist', async () => {
      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('users')) {
          // Return mock user for auth check
          cb(null, [{ id: testUserId, jwt: authToken }]);
        } else {
          // Return empty holdings list
          cb(null, []);
        }
      });

      const response = await request(app).get('/api/v1/holdings').set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.data).toEqual([]);
      expect(db.query).toHaveBeenCalled();
    });

    it('should return 401 when no auth token provided', async () => {
      const response = await request(app).get('/api/v1/holdings');

      expect(response.status).toBe(401);
      expect(response.body.error.code).toBe(ERROR_AUTH.TOKEN);
      expect(db.query).not.toHaveBeenCalled();
    });

    it('should return list of holdings for authenticated user', async () => {
      const mockHoldings = [
        {
          id: '1',
          uid: testUserId,
          count: 100,
          currency: 1,
          dividend: 0.5,
          dividendCurrency: 1,
          dividendFrequency: 4,
          dividendTaxRate: 0.15,
          name: 'Apple Inc.',
          position: 1,
          price: 150.5,
          symbol: 'AAPL',
        },
        {
          id: '2',
          uid: testUserId,
          count: 50,
          currency: 1,
          dividend: 1.25,
          dividendCurrency: 1,
          dividendFrequency: 4,
          dividendTaxRate: 0.15,
          name: 'Microsoft Corporation',
          position: 2,
          price: 285.75,
          symbol: 'MSFT',
        },
      ];

      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('users')) {
          // Return mock user for auth check
          cb(null, [{ id: testUserId, jwt: authToken }]);
        } else {
          // Return mock holdings list
          cb(null, mockHoldings);
        }
      });

      const response = await request(app).get('/api/v1/holdings').set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.data).toEqual(mockHoldings);
      expect(db.query).toHaveBeenCalled();
    });

    it('should pass sort parameter to findAll when provided', async () => {
      const mockHoldings = [
        {
          id: '1',
          uid: testUserId,
          count: 100,
          currency: 1,
          dividend: 0.5,
          dividendCurrency: 1,
          dividendFrequency: 4,
          dividendTaxRate: 0.15,
          name: 'Apple Inc.',
          position: 1,
          price: 150.5,
          symbol: 'AAPL',
        },
        {
          id: '2',
          uid: testUserId,
          count: 50,
          currency: 1,
          dividend: 1.25,
          dividendCurrency: 1,
          dividendFrequency: 4,
          dividendTaxRate: 0.15,
          name: 'Microsoft Corporation',
          position: 2,
          price: 285.75,
          symbol: 'MSFT',
        },
      ];

      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('users')) {
          // Return mock user for auth check
          cb(null, [{ id: testUserId, jwt: authToken }]);
        } else {
          // Return mock holdings list sorted by name
          const sortedHoldings = [...mockHoldings].sort((a, b) => {
            if (query.includes('DESC')) {
              return b.name.localeCompare(a.name);
            }
            return a.name.localeCompare(b.name);
          });
          cb(null, sortedHoldings);
        }
      });

      // Test ascending sort
      const responseAsc = await request(app).get('/api/v1/holdings').query({ sort: 'name' }).set('Authorization', `Bearer ${authToken}`);

      expect(responseAsc.status).toBe(200);
      expect(responseAsc.body.data).toEqual([...mockHoldings].sort((a, b) => a.name.localeCompare(b.name)));
      expect(db.query).toHaveBeenCalled();

      // Test descending sort
      const responseDesc = await request(app).get('/api/v1/holdings').query({ sort: '-name' }).set('Authorization', `Bearer ${authToken}`);

      expect(responseDesc.status).toBe(200);
      expect(responseDesc.body.data).toEqual([...mockHoldings].sort((a, b) => b.name.localeCompare(a.name)));
      expect(db.query).toHaveBeenCalled();
    });
  });

  describe('POST /api/v1/holdings', () => {
    it('should replace with single holding when replace=true', async () => {
      const expectedId = 'uuid-single';
      mockUuid.setValue(expectedId);

      const holdingData = {
        count: 75,
        currency: 1,
        dividend: 0.88,
        dividendCurrency: 1,
        dividendFrequency: 4,
          dividendTaxRate: 0.15,
        name: 'Tesla, Inc.',
        position: 3,
        price: 242.5,
        symbol: 'TSLA',
      };

      const createdHolding = {
        ...holdingData,
        id: expectedId,
        uid: testUserId,
      };

      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('SELECT * FROM users')) {
          cb(null, [{ id: testUserId, jwt: authToken }]);
        } else if (query.includes('DELETE')) {
          cb(null, { affectedRows: 2 }); // Simulating deletion of existing holdings
        } else if (query.includes('INSERT')) {
          cb(null, [createdHolding]);
        } else if (query.includes('SELECT * FROM holdings')) {
          cb(null, [createdHolding]);
        }
      });

      const response = await request(app)
        .post('/api/v1/holdings')
        .query({ replace: 'true' })
        .set('Authorization', `Bearer ${authToken}`)
        .send(holdingData);

      expect(response.status).toBe(201);
      expect(response.body.data).toEqual([createdHolding]);
      expect(db.query).toHaveBeenCalled();
    });

    it('should create a single holding successfully', async () => {
      const expectedId = '3';
      mockUuid.setValue(expectedId);

      const holdingData = {
        count: 75,
        currency: 1,
        dividend: 0.88,
        dividendCurrency: 1,
        dividendFrequency: 4,
          dividendTaxRate: 0.15,
        name: 'Tesla, Inc.',
        position: 3,
        price: 242.5,
        symbol: 'TSLA',
      };

      const createdHolding = {
        ...holdingData,
        id: expectedId,
        uid: testUserId,
      };

      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('users')) {
          // Return mock user for auth check
          cb(null, [{ id: testUserId, jwt: authToken }]);
        } else if (query.includes('INSERT')) {
          // Return the created holding
          cb(null, [createdHolding]);
        }
      });

      const response = await request(app).post('/api/v1/holdings').set('Authorization', `Bearer ${authToken}`).send(holdingData);

      expect(response.status).toBe(201);
      expect(response.body.data).toEqual(createdHolding);
      expect(db.query).toHaveBeenCalled();
    });

    it('should create multiple holdings successfully', async () => {
      const expectedIds = ['uuid-1', 'uuid-2'];
      let currentIdIndex = 0;
      mockUuid.setValue(expectedIds[0]); // Set initial UUID

      const holdingsData = [
        {
          count: 30,
          currency: 1,
          dividend: 0.95,
          dividendCurrency: 1,
          dividendFrequency: 4,
          dividendTaxRate: 0.15,
          name: 'Google Inc.',
          position: 3,
          price: 125.5,
          symbol: 'GOOGL',
        },
        {
          count: 100,
          currency: 1,
          dividend: 0.25,
          dividendCurrency: 1,
          dividendFrequency: 4,
          dividendTaxRate: 0.15,
          name: 'Intel Corp.',
          position: 4,
          price: 35.75,
          symbol: 'INTC',
        },
      ];

      const expectedHoldings = holdingsData.map((data, index) => ({
        ...data,
        id: expectedIds[index],
        uid: testUserId,
      }));

      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('SELECT * FROM users')) {
          cb(null, [{ id: testUserId, jwt: authToken }]);
        } else if (query.includes('INSERT')) {
          // Update UUID for next insert
          const currentHolding = expectedHoldings[currentIdIndex];
          currentIdIndex++;
          if (currentIdIndex < expectedIds.length) {
            mockUuid.setValue(expectedIds[currentIdIndex]);
          }
          cb(null, [currentHolding]);
        } else if (query.includes('SELECT * FROM holdings')) {
          cb(null, expectedHoldings);
        }
      });

      const response = await request(app).post('/api/v1/holdings').set('Authorization', `Bearer ${authToken}`).send(holdingsData);

      expect(response.status).toBe(201);
      expect(response.body.data).toEqual(expectedHoldings);
      expect(db.query).toHaveBeenCalled();
    });

    it('should replace all holdings when replace=true query parameter is provided', async () => {
      const expectedIds = ['uuid-3', 'uuid-4'];
      let currentIdIndex = 0;
      mockUuid.setValue(expectedIds[0]); // Set initial UUID

      const holdingsData = [
        {
          count: 200,
          currency: 1,
          dividend: 0.75,
          dividendCurrency: 1,
          dividendFrequency: 4,
          dividendTaxRate: 0.15,
          name: 'AMD Inc.',
          position: 1,
          price: 95.5,
          symbol: 'AMD',
        },
        {
          count: 150,
          currency: 1,
          dividend: 0.35,
          dividendCurrency: 1,
          dividendFrequency: 4,
          dividendTaxRate: 0.15,
          name: 'Nvidia Corp.',
          position: 2,
          price: 425.75,
          symbol: 'NVDA',
        },
      ];

      const createdHoldings = holdingsData.map((data, index) => ({
        ...data,
        id: expectedIds[index],
        uid: testUserId,
      }));

      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('users')) {
          cb(null, [{ id: testUserId, jwt: authToken }]);
        } else if (query.includes('DELETE')) {
          cb(null, { affectedRows: 2 });
        } else if (query.includes('INSERT')) {
          // Update UUID for next insert
          currentIdIndex++;
          if (currentIdIndex < expectedIds.length) {
            mockUuid.setValue(expectedIds[currentIdIndex]);
          }
          cb(null, []);
        } else if (query.includes('SELECT')) {
          cb(null, createdHoldings);
        }
      });

      const response = await request(app)
        .post('/api/v1/holdings')
        .query({ replace: 'true' })
        .set('Authorization', `Bearer ${authToken}`)
        .send(holdingsData);

      expect(response.status).toBe(201);
      expect(response.body.data).toEqual(createdHoldings);
      expect(db.query).toHaveBeenCalled();
    });

    it('should return 401 when no auth token provided', async () => {
      const holdingData = {
        count: 75,
        currency: 1,
        dividend: 0.88,
        dividendCurrency: 1,
        dividendFrequency: 4,
          dividendTaxRate: 0.15,
        name: 'Tesla, Inc.',
        position: 3,
        price: 242.5,
        symbol: 'TSLA',
      };

      const response = await request(app).post('/api/v1/holdings').send(holdingData);

      expect(response.status).toBe(401);
      expect(response.body.error.code).toBe(ERROR_AUTH.TOKEN);
      expect(db.query).not.toHaveBeenCalled();
    });

    it('should handle validation/database errors', async () => {
      const expectedId = 'error-test-uuid';
      mockUuid.setValue(expectedId);

      const holdingData = {
        count: 75,
        currency: 1,
        dividend: 0.88,
        dividendCurrency: 1,
        dividendFrequency: 4,
          dividendTaxRate: 0.15,
        name: 'Tesla, Inc.',
        position: 3,
        price: 242.5,
        symbol: 'TSLA',
      };

      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('users')) {
          // Return mock user for auth check
          cb(null, [{ id: testUserId, jwt: authToken }]);
        } else if (query.includes('INSERT')) {
          // Simulate database error
          cb(new Error('Database connection failed'));
        }
      });

      const response = await request(app).post('/api/v1/holdings').set('Authorization', `Bearer ${authToken}`).send(holdingData);

      expect(response.status).toBe(500);
      expect(response.body.error).toEqual({
        code: ERROR_HOLDING.CREATE,
        message: 'Database connection failed',
      });
      expect(db.query).toHaveBeenCalled();
    });

    it('should handle invalid holding data', async () => {
      const invalidData = {
        count: 75,
        currency: 1,
        dividend: 0.88,
        dividendCurrency: 1,
        dividendFrequency: 4,
          dividendTaxRate: 0.15,
        position: 3,
        price: 242.5,
        // missing name and symbol
      };

      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('SELECT * FROM users')) {
          cb(null, [{ id: testUserId, jwt: authToken }]);
        }
      });

      const response = await request(app).post('/api/v1/holdings').set('Authorization', `Bearer ${authToken}`).send(invalidData);

      expect(response.status).toBe(400);
      expect(response.body.error.code).toBe(ERROR_HOLDING.PATCH_TYPE_ALL);
      expect(response.body.error.message).toBe('Invalid holding data. Holding must have name and symbol properties.');
      expect(db.query).toHaveBeenCalledTimes(1);
      expect(db.query).toHaveBeenCalledWith(
        'SELECT * FROM users WHERE `id`=? AND `jwt`=? LIMIT 1;',
        [testUserId, authToken],
        expect.any(Function),
      );
    });

    it('should handle invalid holdings in array', async () => {
      const invalidData = [
        {
          count: 30,
          currency: 1,
          dividend: 0.95,
          dividendCurrency: 1,
          dividendFrequency: 4,
          dividendTaxRate: 0.15,
          position: 3,
          price: 125.5,
          // missing name and symbol
        },
        {
          count: 100,
          currency: 1,
          dividend: 0.25,
          dividendCurrency: 1,
          dividendFrequency: 4,
          dividendTaxRate: 0.15,
          position: 4,
          price: 35.75,
          name: 'Intel Corp.',
          // missing symbol
        },
      ];

      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('SELECT * FROM users')) {
          cb(null, [{ id: testUserId, jwt: authToken }]);
        }
      });

      const response = await request(app).post('/api/v1/holdings').set('Authorization', `Bearer ${authToken}`).send(invalidData);

      expect(response.status).toBe(400);
      expect(response.body.error.code).toBe(ERROR_HOLDING.PATCH_TYPE_ALL);
      expect(response.body.error.message).toBe('Invalid holding data. Each holding must have name and symbol properties.');
      expect(db.query).toHaveBeenCalledTimes(1);
      expect(db.query).toHaveBeenCalledWith(
        'SELECT * FROM users WHERE `id`=? AND `jwt`=? LIMIT 1;',
        [testUserId, authToken],
        expect.any(Function),
      );
    });
  });

  describe('PATCH /api/v1/holdings', () => {
    it('should update multiple holdings successfully', async () => {
      const holdingsData = [
        {
          id: '1',
          count: 120,
          price: 155.75,
          name: 'Apple Inc.',
          symbol: 'AAPL',
        },
        {
          id: '2',
          count: 60,
          price: 290.25,
          name: 'Microsoft Corporation',
          symbol: 'MSFT',
        },
      ];

      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('users')) {
          cb(null, [{ id: testUserId, jwt: authToken }]);
        } else if (query.includes('UPDATE')) {
          cb(null, { affectedRows: 1 });
        } else if (query.includes('SELECT')) {
          cb(
            null,
            holdingsData.map((holding) => ({
              ...holding,
              currency: 1,
              dividend: 0.5,
              dividendCurrency: 1,
              dividendFrequency: 4,
          dividendTaxRate: 0.15,
              position: Number(holding.id),
              uid: testUserId,
            })),
          );
        }
      });

      const response = await request(app).patch('/api/v1/holdings').set('Authorization', `Bearer ${authToken}`).send(holdingsData);

      expect(response.status).toBe(200);
      expect(response.body.data).toHaveLength(2);
      expect(response.body.data[0]).toMatchObject(holdingsData[0]);
      expect(response.body.data[1]).toMatchObject(holdingsData[1]);
      expect(db.query).toHaveBeenCalled();
    });

    it('should update single holding through bulk endpoint', async () => {
      const holdingData = {
        id: '1',
        count: 120,
        price: 155.75,
        name: 'Apple Inc.',
        symbol: 'AAPL',
      };

      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('users')) {
          cb(null, [{ id: testUserId, jwt: authToken }]);
        } else if (query.includes('UPDATE')) {
          cb(null, { affectedRows: 1 });
        } else if (query.includes('SELECT')) {
          cb(null, [
            {
              ...holdingData,
              currency: 1,
              dividend: 0.5,
              dividendCurrency: 1,
              dividendFrequency: 4,
          dividendTaxRate: 0.15,
              position: 1,
              uid: testUserId,
            },
          ]);
        }
      });

      const response = await request(app).patch('/api/v1/holdings').set('Authorization', `Bearer ${authToken}`).send([holdingData]);

      expect(response.status).toBe(200);
      expect(response.body.data).toHaveLength(1);
      expect(response.body.data[0]).toMatchObject(holdingData);
      expect(db.query).toHaveBeenCalled();
    });

    it('should update a single holding when non-array object is sent', async () => {
      const holdingData = {
        id: '1',
        count: 130,
        price: 165.25,
        name: 'Apple Inc.',
        symbol: 'AAPL',
      };

      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('users')) {
          cb(null, [{ id: testUserId, jwt: authToken }]);
        } else if (query.includes('UPDATE')) {
          cb(null, { affectedRows: 1 });
        }
      });

      const response = await request(app).patch('/api/v1/holdings').set('Authorization', `Bearer ${authToken}`).send(holdingData);

      // This specific branch returns 204 with empty JSON
      expect(response.status).toBe(204);
      expect(response.body).toEqual({});
      expect(db.query).toHaveBeenCalled();

      // Verify the query contained the holding ID and user ID
      expect(db.query).toHaveBeenCalledWith(
        expect.stringContaining('UPDATE holdings'),
        expect.arrayContaining([holdingData.id, testUserId]),
        expect.any(Function),
      );
    });

    it('should return 401 when no auth token provided', async () => {
      const holdingData = [
        {
          id: '1',
          count: 120,
          price: 155.75,
          name: 'Apple Inc.',
          symbol: 'AAPL',
        },
      ];

      const response = await request(app).patch('/api/v1/holdings').send(holdingData);

      expect(response.status).toBe(401);
      expect(response.body.error.code).toBe(ERROR_AUTH.TOKEN);
      expect(db.query).not.toHaveBeenCalled();
    });

    it('should handle validation/database errors', async () => {
      const holdingData = [
        {
          id: '1',
          count: 120,
          price: 155.75,
          name: 'Apple Inc.',
          symbol: 'AAPL',
        },
      ];

      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('users')) {
          cb(null, [{ id: testUserId, jwt: authToken }]);
        } else if (query.includes('SELECT')) {
          cb(null, [
            {
              ...holdingData[0],
              uid: testUserId,
              currency: 1,
              dividend: 0.5,
              dividendCurrency: 1,
              dividendFrequency: 4,
          dividendTaxRate: 0.15,
              position: 1,
            },
          ]);
        } else if (query.includes('UPDATE')) {
          cb(new Error('Database connection failed')); // Pass error as first argument
        }
      });

      const response = await request(app).patch('/api/v1/holdings').set('Authorization', `Bearer ${authToken}`).send(holdingData);

      expect(response.status).toBe(500);
      expect(response.body.error).toEqual({
        code: ERROR_HOLDING.PATCH_ALL,
        message: 'Database connection failed',
      });
      expect(db.query).toHaveBeenCalled();
    });

    it('should update single holding', async () => {
      const holdingId = '1';
      const updateData = {
        count: 150,
        price: 160.25,
        position: 2,
        name: 'Apple Inc.',
        symbol: 'AAPL',
      };

      const existingHolding = {
        id: holdingId,
        uid: testUserId,
        count: 100,
        currency: 1,
        dividend: 0.5,
        dividendCurrency: 1,
        dividendFrequency: 4,
          dividendTaxRate: 0.15,
        name: 'Apple Inc.',
        position: 1,
        price: 150.5,
        symbol: 'AAPL',
      };

      const updatedHolding = {
        ...existingHolding,
        ...updateData,
      };

      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('users')) {
          cb(null, [{ id: testUserId, jwt: authToken }]);
        } else if (query.includes('SELECT')) {
          cb(null, [existingHolding]);
        } else if (query.includes('UPDATE')) {
          cb(null, [updatedHolding]);
        }
      });

      const response = await request(app)
        .patch(`/api/v1/holdings/${holdingId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send(updateData);

      expect(response.status).toBe(200);
      expect(response.body.data).toMatchObject(updatedHolding);
      expect(db.query).toHaveBeenCalled();
    });

    it('should handle error when updating single holding', async () => {
      const holdingData = {
        id: '1',
        count: 150,
        price: 160.25,
        name: 'Apple Inc.',
        symbol: 'AAPL',
      };

      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('SELECT * FROM users')) {
          cb(null, [{ id: testUserId, jwt: authToken }]);
        } else if (query.includes('UPDATE holdings')) {
          cb(new Error('Database connection failed'));
        }
      });

      const response = await request(app).patch('/api/v1/holdings').set('Authorization', `Bearer ${authToken}`).send(holdingData);

      expect(response.status).toBe(500);
      expect(response.body.error.code).toBe(ERROR_HOLDING.PATCH);
      expect(response.body.error.message).toBe('Database connection failed');
      expect(db.query).toHaveBeenCalled();
    });

    it('should handle invalid input type', async () => {
      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('SELECT * FROM users')) {
          cb(null, [{ id: testUserId, jwt: authToken }]);
        }
      });

      const response = await request(app).patch('/api/v1/holdings').set('Authorization', `Bearer ${authToken}`).send({});

      expect(response.status).toBe(400);
      expect(response.body.error.code).toBe(ERROR_HOLDING.PATCH_TYPE_ALL);
      expect(response.body.error.message).toBe('Invalid holding data. Holding must have name and symbol properties.');
      expect(db.query).toHaveBeenCalledTimes(1);
      expect(db.query).toHaveBeenCalledWith(
        'SELECT * FROM users WHERE `id`=? AND `jwt`=? LIMIT 1;',
        [testUserId, authToken],
        expect.any(Function),
      );
    });

    it('should handle invalid holdings in array', async () => {
      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('SELECT * FROM users')) {
          cb(null, [{ id: testUserId, jwt: authToken }]);
        }
      });

      const invalidHoldingsData = [
        {
          id: '1',
          count: 120,
          price: 155.75,
          // missing name and symbol
        },
        {
          id: '2',
          count: 60,
          price: 290.25,
          name: 'Microsoft Corporation',
          // missing symbol
        },
      ];

      const response = await request(app).patch('/api/v1/holdings').set('Authorization', `Bearer ${authToken}`).send(invalidHoldingsData);

      expect(response.status).toBe(400);
      expect(response.body.error.code).toBe(ERROR_HOLDING.PATCH_TYPE_ALL);
      expect(response.body.error.message).toBe('Invalid holding data. Each holding must have name and symbol properties.');
      expect(db.query).toHaveBeenCalledTimes(1);
      expect(db.query).toHaveBeenCalledWith(
        'SELECT * FROM users WHERE `id`=? AND `jwt`=? LIMIT 1;',
        [testUserId, authToken],
        expect.any(Function),
      );
    });
  });

  describe('PATCH /api/v1/holdings/:id', () => {
    it('should handle invalid holding data', async () => {
      const holdingId = '1';
      const invalidData = {
        count: 150,
        price: 160.25,
        // missing name and symbol
      };

      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('SELECT * FROM users')) {
          cb(null, [{ id: testUserId, jwt: authToken }]);
        }
      });

      const response = await request(app)
        .patch(`/api/v1/holdings/${holdingId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send(invalidData);

      expect(response.status).toBe(400);
      expect(response.body.error.code).toBe(ERROR_HOLDING.PATCH_TYPE_ALL);
      expect(response.body.error.message).toBe('Invalid holding data. Holding must have name and symbol properties.');
      expect(db.query).toHaveBeenCalledTimes(1);
      expect(db.query).toHaveBeenCalledWith(
        'SELECT * FROM users WHERE `id`=? AND `jwt`=? LIMIT 1;',
        [testUserId, authToken],
        expect.any(Function),
      );
    });

    it('should update specific holding fields successfully', async () => {
      const holdingId = '1';
      const updateData = {
        count: 150,
        price: 160.25,
        position: 2,
        name: 'Apple Inc.',
        symbol: 'AAPL',
      };

      const existingHolding = {
        id: holdingId,
        uid: testUserId,
        count: 100,
        currency: 1,
        dividend: 0.5,
        dividendCurrency: 1,
        dividendFrequency: 4,
          dividendTaxRate: 0.15,
        name: 'Apple Inc.',
        position: 1,
        price: 150.5,
        symbol: 'AAPL',
      };

      const updatedHolding = {
        ...existingHolding,
        ...updateData,
      };

      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('users')) {
          cb(null, [{ id: testUserId, jwt: authToken }]);
        } else if (query.includes('SELECT')) {
          cb(null, [existingHolding]);
        } else if (query.includes('UPDATE')) {
          cb(null, [updatedHolding]);
        }
      });

      const response = await request(app)
        .patch(`/api/v1/holdings/${holdingId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send(updateData);

      expect(response.status).toBe(200);
      expect(response.body.data).toMatchObject(updatedHolding);
      expect(db.query).toHaveBeenCalled();
    });

    it('should return 404 for non-existent holding', async () => {
      const holdingId = 'non-existent';
      const updateData = {
        count: 150,
        price: 160.25,
        name: 'Apple Inc.',
        symbol: 'AAPL',
      };

      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('users')) {
          cb(null, [{ id: testUserId, jwt: authToken }]);
        } else if (query.includes('SELECT')) {
          // Return empty result to simulate non-existent holding
          cb(null, []);
        }
      });

      const response = await request(app)
        .patch(`/api/v1/holdings/${holdingId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send(updateData);

      expect(response.status).toBe(404);
      expect(response.body.error.code).toBe(ERROR_HOLDING.NOT_FOUND);
      expect(db.query).toHaveBeenCalled();
    });

    it('should return 401 when no auth token provided', async () => {
      const holdingId = '1';
      const updateData = {
        count: 150,
        price: 160.25,
        name: 'Apple Inc.',
        symbol: 'AAPL',
      };

      const response = await request(app).patch(`/api/v1/holdings/${holdingId}`).send(updateData);

      expect(response.status).toBe(401);
      expect(response.body.error.code).toBe(ERROR_AUTH.TOKEN);
      expect(db.query).not.toHaveBeenCalled();
    });

    it('should handle validation/database errors', async () => {
      const holdingId = '1';
      const expectedId = 'error-test-uuid';
      mockUuid.setValue(expectedId);

      const updateData = {
        count: 150,
        price: 160.25,
        name: 'Apple Inc.',
        symbol: 'AAPL',
      };

      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('users')) {
          cb(null, [{ id: testUserId, jwt: authToken }]);
        } else if (query.includes('SELECT')) {
          cb(null, [
            {
              id: holdingId,
              uid: testUserId,
              count: 100,
              price: 150.5,
              name: 'Apple Inc.',
              symbol: 'AAPL',
            },
          ]);
        } else if (query.includes('UPDATE')) {
          cb(new Error('Database connection failed'));
        }
      });

      const response = await request(app)
        .patch(`/api/v1/holdings/${holdingId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send(updateData);

      expect(response.status).toBe(500);
      expect(response.body.error).toEqual({
        code: ERROR_HOLDING.UPDATE,
        message: 'Database connection failed',
      });
      expect(db.query).toHaveBeenCalled();
    });
  });

  describe('DELETE /api/v1/holdings/:id', () => {
    it('should delete a holding successfully', async () => {
      const holdingId = '1';

      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('users')) {
          cb(null, [{ id: testUserId, jwt: authToken }]);
        } else if (query.includes('DELETE')) {
          cb(null, { affectedRows: 1 });
        } else if (query.includes('SELECT')) {
          cb(null, [
            {
              id: holdingId,
              uid: testUserId,
              count: 100,
              currency: 1,
              dividend: 0.5,
              dividendCurrency: 1,
              dividendFrequency: 4,
          dividendTaxRate: 0.15,
              name: 'Apple Inc.',
              position: 1,
              price: 150.5,
              symbol: 'AAPL',
            },
          ]);
        }
      });

      const response = await request(app).delete(`/api/v1/holdings/${holdingId}`).set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(204);
      expect(response.body).toEqual({});
      expect(db.query).toHaveBeenCalled();
    });

    it('should return 404 for non-existent holding', async () => {
      const holdingId = 'non-existent';

      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('users')) {
          cb(null, [{ id: testUserId, jwt: authToken }]);
        } else if (query.includes('SELECT')) {
          cb(null, []);
        } else if (query.includes('DELETE')) {
          cb(null, { affectedRows: 0 });
        }
      });

      const response = await request(app).delete(`/api/v1/holdings/${holdingId}`).set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(404);
      expect(response.body.error.code).toBe(ERROR_HOLDING.NOT_FOUND);
      expect(db.query).toHaveBeenCalled();
    });

    it('should return 401 when no auth token provided', async () => {
      const holdingId = '1';

      const response = await request(app).delete(`/api/v1/holdings/${holdingId}`);

      expect(response.status).toBe(401);
      expect(response.body.error.code).toBe(ERROR_AUTH.TOKEN);
      expect(db.query).not.toHaveBeenCalled();
    });

    it('should handle database errors', async () => {
      const holdingId = '1';

      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('users')) {
          cb(null, [{ id: testUserId, jwt: authToken }]);
        } else if (query.includes('SELECT')) {
          cb(null, [
            {
              id: holdingId,
              uid: testUserId,
            },
          ]);
        } else if (query.includes('DELETE')) {
          cb(new Error('Database connection failed'));
        }
      });

      const response = await request(app).delete(`/api/v1/holdings/${holdingId}`).set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(500);
      expect(response.body.error).toEqual({
        code: ERROR_HOLDING.DELETE,
        message: 'Database connection failed',
      });
      expect(db.query).toHaveBeenCalled();
    });
  });
});
