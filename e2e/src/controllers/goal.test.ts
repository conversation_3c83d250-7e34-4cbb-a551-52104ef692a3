import { Express } from 'express';
import { Server } from 'http';
import request from 'supertest';
import db from '@api/connection';

import { Auth } from '@api/services';
import { ERROR_AUTH, ERROR_GOAL } from '@api/lib/constants/error-codes';
import { createTestServer } from '@e2e/support/server-test-utils';

// Mock database connection
jest.mock('@api/connection');

// Mock jsonwebtoken
jest.mock('jsonwebtoken');

// Mock UUID
const mockUuid = {
  value: '',
  setValue: function (newValue: string) {
    if (!newValue) {
      throw new Error('UUID must be explicitly set in each test');
    }
    this.value = newValue;
  },
  getValue: function () {
    if (!this.value) {
      throw new Error('UUID must be explicitly set before using it');
    }
    return this.value;
  },
};

jest.mock('@api/lib/utils/uuid', () => ({
  __esModule: true,
  default: jest.fn().mockImplementation(() => mockUuid.getValue()),
}));

describe('Controllers | Goal', () => {
  let app: Express;
  let server: Server;
  let authToken: string;
  const testUserId = 'test-user';

  beforeAll(async () => {
    const { app: testApp, server: testServer } = createTestServer();
    app = testApp;
    server = testServer;

    const auth = Auth();
    authToken = auth.sign(testUserId);

    // Mock user auth check
    // @ts-expect-error - mockImplementation is jest fn not db query
    db.query.mockImplementation((query, params, cb) => {
      if (query.includes('SELECT * FROM users')) {
        cb(null, [{ id: testUserId, jwt: authToken }]);
      }
    });
  });

  afterAll((done) => {
    if (server) {
      server.close(done);
    } else {
      done();
    }
  });

  beforeEach(() => {
    jest.clearAllMocks();
    mockUuid.value = ''; // Reset UUID for each test to force explicit setting
  });

  afterEach(() => {
    jest.restoreAllMocks();
    // @ts-expect-error - mockClear is jest fn not db query
    db.query.mockClear(); // reset mock call count
  });

  describe('GET /api/v1/goals', () => {
    it('should handle database errors', async () => {
      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('SELECT * FROM users')) {
          cb(null, [{ id: testUserId, jwt: authToken }]);
        } else if (query.includes('SELECT * FROM goals')) {
          cb(new Error('Database connection failed'));
        }
      });

      const response = await request(app).get('/api/v1/goals').set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(500);
      expect(response.body.error.code).toBe(ERROR_GOAL.FETCH);
      expect(response.body.error.message).toBe('Database connection failed');
      expect(db.query).toHaveBeenCalledTimes(2);
      expect(db.query).toHaveBeenNthCalledWith(
        1,
        'SELECT * FROM users WHERE `id`=? AND `jwt`=? LIMIT 1;',
        [testUserId, authToken],
        expect.any(Function),
      );
    });

    it('should return empty list when no goals exist', async () => {
      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('users')) {
          cb(null, [{ id: testUserId, jwt: authToken }]);
        } else {
          cb(null, []);
        }
      });

      const response = await request(app).get('/api/v1/goals').set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.data).toEqual([]);
      expect(db.query).toHaveBeenCalled();
    });

    it('should return 401 when no auth token provided', async () => {
      const response = await request(app).get('/api/v1/goals');

      expect(response.status).toBe(401);
      expect(response.body.error.code).toBe(ERROR_AUTH.TOKEN);
      expect(db.query).not.toHaveBeenCalled();
    });

    it('should return list of goals for authenticated user', async () => {
      const mockGoals = [
        {
          id: '1',
          uid: testUserId,
          currency: 1,
          dividends: 100,
          networth: 1000,
        },
        {
          id: '2',
          uid: testUserId,
          currency: 1,
          dividends: 200,
          networth: 2000,
        },
      ];

      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('users')) {
          cb(null, [{ id: testUserId, jwt: authToken }]);
        } else {
          cb(null, mockGoals);
        }
      });

      const response = await request(app).get('/api/v1/goals').set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.data).toEqual(mockGoals);
      expect(db.query).toHaveBeenCalled();
    });
  });

  describe('POST /api/v1/goals', () => {
    it('should create a single goal successfully', async () => {
      const expectedId = '3';
      mockUuid.setValue(expectedId);

      const goalData = {
        currency: 1,
        dividends: 150,
        networth: 1500,
      };

      const createdGoal = {
        ...goalData,
        id: expectedId,
        uid: testUserId,
      };

      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('users')) {
          cb(null, [{ id: testUserId, jwt: authToken }]);
        } else if (query.includes('INSERT')) {
          cb(null, [createdGoal]);
        }
      });

      const response = await request(app).post('/api/v1/goals').set('Authorization', `Bearer ${authToken}`).send(goalData);

      expect(response.status).toBe(201);
      expect(response.body.data).toEqual(createdGoal);
      expect(db.query).toHaveBeenCalled();
    });

    it('should create multiple goals successfully', async () => {
      const expectedIds = ['uuid-1', 'uuid-2'];
      let currentIdIndex = 0;
      mockUuid.setValue(expectedIds[0]); // Set initial UUID

      const goalsData = [
        {
          currency: 1,
          dividends: 150,
          networth: 1500,
        },
        {
          currency: 1,
          dividends: 250,
          networth: 2500,
        },
      ];

      const expectedGoals = goalsData.map((data, index) => ({
        ...data,
        id: expectedIds[index],
        uid: testUserId,
      }));

      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('SELECT * FROM users')) {
          cb(null, [{ id: testUserId, jwt: authToken }]);
        } else if (query.includes('INSERT')) {
          const currentGoal = expectedGoals[currentIdIndex];
          currentIdIndex++;
          if (currentIdIndex < expectedIds.length) {
            mockUuid.setValue(expectedIds[currentIdIndex]);
          }
          cb(null, [currentGoal]);
        } else if (query.includes('SELECT * FROM goals')) {
          cb(null, expectedGoals);
        }
      });

      const response = await request(app).post('/api/v1/goals').set('Authorization', `Bearer ${authToken}`).send(goalsData);

      expect(response.status).toBe(201);
      expect(response.body.data).toEqual(expectedGoals);
      expect(db.query).toHaveBeenCalled();
    });

    it('should replace all goals when replace=true', async () => {
      const expectedIds = ['uuid-3', 'uuid-4'];
      let currentIdIndex = 0;
      mockUuid.setValue(expectedIds[0]); // Set initial UUID

      const goalsData = [
        {
          currency: 1,
          dividends: 150,
          networth: 1500,
        },
        {
          currency: 1,
          dividends: 250,
          networth: 2500,
        },
      ];

      const createdGoals = goalsData.map((data, index) => ({
        ...data,
        id: expectedIds[index],
        uid: testUserId,
      }));

      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('users')) {
          cb(null, [{ id: testUserId, jwt: authToken }]);
        } else if (query.includes('DELETE')) {
          cb(null, { affectedRows: 2 });
        } else if (query.includes('INSERT')) {
          currentIdIndex++;
          if (currentIdIndex < expectedIds.length) {
            mockUuid.setValue(expectedIds[currentIdIndex]);
          }
          cb(null, []);
        } else if (query.includes('SELECT')) {
          cb(null, createdGoals);
        }
      });

      const response = await request(app)
        .post('/api/v1/goals')
        .query({ replace: 'true' })
        .set('Authorization', `Bearer ${authToken}`)
        .send(goalsData);

      expect(response.status).toBe(201);
      expect(response.body.data).toEqual(createdGoals);
      expect(db.query).toHaveBeenCalled();
    });

    it('should replace goals with a single goal when replace=true and input is object', async () => {
      const expectedId = 'uuid-5';
      mockUuid.setValue(expectedId);

      const goalData = {
        currency: 1,
        dividends: 150,
        networth: 1500,
      };

      const createdGoal = {
        ...goalData,
        id: expectedId,
        uid: testUserId,
      };

      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('users')) {
          cb(null, [{ id: testUserId, jwt: authToken }]);
        } else if (query.includes('DELETE')) {
          // Mock deleting existing goals
          cb(null, { affectedRows: 2 });
        } else if (query.includes('INSERT')) {
          // Mock inserting new goal
          cb(null, [createdGoal]);
        } else if (query.includes('SELECT')) {
          // Mock retrieving all goals (after creation)
          cb(null, [createdGoal]);
        }
      });

      const response = await request(app)
        .post('/api/v1/goals')
        .query({ replace: 'true' })
        .set('Authorization', `Bearer ${authToken}`)
        .send(goalData);

      expect(response.status).toBe(201);
      expect(response.body.data).toEqual([createdGoal]);
      expect(db.query).toHaveBeenCalled();
      // Verify that DELETE was called to clear existing goals
      expect(db.query).toHaveBeenCalledWith('DELETE FROM goals WHERE `uid`=?;', [testUserId], expect.any(Function));
    });

    it('should return 401 when no auth token provided', async () => {
      const goalData = {
        currency: 1,
        dividends: 150,
        networth: 1500,
      };

      const response = await request(app).post('/api/v1/goals').send(goalData);

      expect(response.status).toBe(401);
      expect(response.body.error.code).toBe(ERROR_AUTH.TOKEN);
      expect(db.query).not.toHaveBeenCalled();
    });

    it('should handle validation/database errors', async () => {
      const expectedId = 'error-test-uuid';
      mockUuid.setValue(expectedId);

      const goalData = {
        currency: 1,
        dividends: 150,
        networth: 1500,
      };

      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('users')) {
          cb(null, [{ id: testUserId, jwt: authToken }]);
        } else if (query.includes('INSERT')) {
          cb(new Error('Database connection failed'));
        }
      });

      const response = await request(app).post('/api/v1/goals').set('Authorization', `Bearer ${authToken}`).send(goalData);

      expect(response.status).toBe(500);
      expect(response.body.error).toEqual({
        code: ERROR_GOAL.CREATE,
        message: 'Database connection failed',
      });
      expect(db.query).toHaveBeenCalled();
    });

    it('should handle invalid goal data', async () => {
      const invalidData = {
        currency: 1,
        // missing networth and dividends
      };

      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('SELECT * FROM users')) {
          cb(null, [{ id: testUserId, jwt: authToken }]);
        }
      });

      const response = await request(app).post('/api/v1/goals').set('Authorization', `Bearer ${authToken}`).send(invalidData);

      expect(response.status).toBe(400);
      expect(response.body.error.code).toBe(ERROR_GOAL.PATCH_TYPE_ALL);
      expect(response.body.error.message).toBe('Invalid goal data. Goal must have networth and dividends properties.');
      expect(db.query).toHaveBeenCalledTimes(1);
      expect(db.query).toHaveBeenCalledWith(
        'SELECT * FROM users WHERE `id`=? AND `jwt`=? LIMIT 1;',
        [testUserId, authToken],
        expect.any(Function),
      );
    });

    it('should handle invalid goals in array', async () => {
      const invalidData = [
        {
          currency: 1,
          // missing networth and dividends
        },
        {
          currency: 1,
          networth: 1500,
          // missing dividends
        },
      ];

      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('SELECT * FROM users')) {
          cb(null, [{ id: testUserId, jwt: authToken }]);
        }
      });

      const response = await request(app).post('/api/v1/goals').set('Authorization', `Bearer ${authToken}`).send(invalidData);

      expect(response.status).toBe(400);
      expect(response.body.error.code).toBe(ERROR_GOAL.PATCH_TYPE_ALL);
      expect(response.body.error.message).toBe('Invalid goal data. Each goal must have networth and dividends properties.');
      expect(db.query).toHaveBeenCalledTimes(1);
      expect(db.query).toHaveBeenCalledWith(
        'SELECT * FROM users WHERE `id`=? AND `jwt`=? LIMIT 1;',
        [testUserId, authToken],
        expect.any(Function),
      );
    });
  });

  describe('PATCH /api/v1/goals/:id', () => {
    it('should update goal fields successfully', async () => {
      const goalId = '1';
      const updateData = {
        currency: 2,
        dividends: 300,
        networth: 3000,
      };

      const existingGoal = {
        id: goalId,
        uid: testUserId,
        currency: 1,
        dividends: 150,
        networth: 1500,
      };

      const updatedGoal = {
        ...existingGoal,
        ...updateData,
      };

      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('users')) {
          cb(null, [{ id: testUserId, jwt: authToken }]);
        } else if (query.includes('SELECT')) {
          cb(null, [existingGoal]);
        } else if (query.includes('UPDATE')) {
          cb(null, [updatedGoal]);
        }
      });

      const response = await request(app).patch(`/api/v1/goals/${goalId}`).set('Authorization', `Bearer ${authToken}`).send(updateData);

      expect(response.status).toBe(200);
      expect(response.body.data).toMatchObject(updatedGoal);
      expect(db.query).toHaveBeenCalled();
    });

    it('should return 404 for non-existent goal', async () => {
      const goalId = 'non-existent';
      const updateData = {
        currency: 2,
        dividends: 300,
        networth: 3000,
      };

      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('users')) {
          cb(null, [{ id: testUserId, jwt: authToken }]);
        } else if (query.includes('SELECT')) {
          cb(null, []);
        }
      });

      const response = await request(app).patch(`/api/v1/goals/${goalId}`).set('Authorization', `Bearer ${authToken}`).send(updateData);

      expect(response.status).toBe(404);
      expect(response.body.error.code).toBe(ERROR_GOAL.NOT_FOUND);
      expect(db.query).toHaveBeenCalled();
    });

    it('should return 401 when no auth token provided', async () => {
      const goalId = '1';
      const updateData = {
        currency: 2,
        dividends: 300,
        networth: 3000,
      };

      const response = await request(app).patch(`/api/v1/goals/${goalId}`).send(updateData);

      expect(response.status).toBe(401);
      expect(response.body.error.code).toBe(ERROR_AUTH.TOKEN);
      expect(db.query).not.toHaveBeenCalled();
    });

    it('should handle validation/database errors', async () => {
      const goalId = '1';
      const updateData = {
        currency: 2,
        dividends: 300,
        networth: 3000,
      };

      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('users')) {
          cb(null, [{ id: testUserId, jwt: authToken }]);
        } else if (query.includes('SELECT')) {
          cb(null, [
            {
              id: goalId,
              uid: testUserId,
              currency: 1,
              dividends: 150,
              networth: 1500,
            },
          ]);
        } else if (query.includes('UPDATE')) {
          cb(new Error('Database connection failed'));
        }
      });

      const response = await request(app).patch(`/api/v1/goals/${goalId}`).set('Authorization', `Bearer ${authToken}`).send(updateData);

      expect(response.status).toBe(500);
      expect(response.body.error).toEqual({
        code: ERROR_GOAL.UPDATE,
        message: 'Database connection failed',
      });
      expect(db.query).toHaveBeenCalled();
    });

    it('should handle invalid goal data', async () => {
      const goalId = '1';
      const invalidData = {
        currency: 2,
        // missing networth and dividends
      };

      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('SELECT * FROM users')) {
          cb(null, [{ id: testUserId, jwt: authToken }]);
        }
      });

      const response = await request(app).patch(`/api/v1/goals/${goalId}`).set('Authorization', `Bearer ${authToken}`).send(invalidData);

      expect(response.status).toBe(400);
      expect(response.body.error.code).toBe(ERROR_GOAL.PATCH_TYPE_ALL);
      expect(response.body.error.message).toBe('Invalid goal data. Goal must have networth and dividends properties.');
      expect(db.query).toHaveBeenCalledTimes(1);
      expect(db.query).toHaveBeenCalledWith(
        'SELECT * FROM users WHERE `id`=? AND `jwt`=? LIMIT 1;',
        [testUserId, authToken],
        expect.any(Function),
      );
    });
  });

  describe('DELETE /api/v1/goals/:id', () => {
    it('should delete a goal successfully', async () => {
      const goalId = '1';

      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('users')) {
          cb(null, [{ id: testUserId, jwt: authToken }]);
        } else if (query.includes('DELETE')) {
          cb(null, { affectedRows: 1 });
        } else if (query.includes('SELECT')) {
          cb(null, [
            {
              id: goalId,
              uid: testUserId,
              currency: 1,
              dividends: 150,
              networth: 1500,
            },
          ]);
        }
      });

      const response = await request(app).delete(`/api/v1/goals/${goalId}`).set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(204);
      expect(response.body).toEqual({});
      expect(db.query).toHaveBeenCalled();
    });

    it('should return 404 for non-existent goal', async () => {
      const goalId = 'non-existent';

      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('users')) {
          cb(null, [{ id: testUserId, jwt: authToken }]);
        } else if (query.includes('SELECT')) {
          cb(null, []);
        }
      });

      const response = await request(app).delete(`/api/v1/goals/${goalId}`).set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(404);
      expect(response.body.error.code).toBe(ERROR_GOAL.NOT_FOUND);
      expect(db.query).toHaveBeenCalled();
    });

    it('should return 401 when no auth token provided', async () => {
      const goalId = '1';

      const response = await request(app).delete(`/api/v1/goals/${goalId}`);

      expect(response.status).toBe(401);
      expect(response.body.error.code).toBe(ERROR_AUTH.TOKEN);
      expect(db.query).not.toHaveBeenCalled();
    });

    it('should handle database errors', async () => {
      const goalId = '1';

      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('users')) {
          cb(null, [{ id: testUserId, jwt: authToken }]);
        } else if (query.includes('SELECT')) {
          cb(null, [
            {
              id: goalId,
              uid: testUserId,
              currency: 1,
              dividends: 150,
              networth: 1500,
            },
          ]);
        } else if (query.includes('DELETE')) {
          cb(new Error('Database connection failed'));
        }
      });

      const response = await request(app).delete(`/api/v1/goals/${goalId}`).set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(500);
      expect(response.body.error).toEqual({
        code: ERROR_GOAL.DELETE,
        message: 'Database connection failed',
      });
      expect(db.query).toHaveBeenCalled();
    });
  });
});
