// Mock database connection
jest.mock('@api/connection');

// Mock jsonwebtoken
jest.mock('jsonwebtoken');

// Mock UUID
const mockUuid = {
  value: '',
  setValue: function (newValue: string) {
    if (!newValue) {
      throw new Error('UUID must be explicitly set in each test');
    }
    this.value = newValue;
  },
  getValue: function () {
    if (!this.value) {
      throw new Error('UUID must be explicitly set before using it');
    }
    return this.value;
  },
};

jest.mock('@api/lib/utils/uuid', () => ({
  __esModule: true,
  default: jest.fn().mockImplementation(() => mockUuid.getValue()),
}));

import { Express } from 'express';
import { Server } from 'http';
import request from 'supertest';
import db from '@api/connection';

import { Auth } from '@api/services';
import { ERROR_AUTH, ERROR_REPORT } from '@api/lib/constants/error-codes';
import { createTestServer } from '@e2e/support/server-test-utils';

describe('Controllers | Report', () => {
  let app: Express;
  let server: Server;
  let authToken: string;
  const testUserId = 'test-user';

  beforeAll(async () => {
    const { app: testApp, server: testServer } = createTestServer();
    app = testApp;
    server = testServer;

    const auth = Auth();
    authToken = auth.sign(testUserId);
  });

  afterAll((done) => {
    if (server) {
      server.close(done);
    } else {
      done();
    }
  });

  beforeEach(() => {
    jest.clearAllMocks();
    mockUuid.value = ''; // Reset UUID for each test to force explicit setting
  });

  afterEach(() => {
    jest.restoreAllMocks();
    // @ts-expect-error - mockClear is jest fn not db query
    db.query.mockClear(); // reset mock call count
  });

  /*
   * Test Plan:
   *
   * GET /api/v1/reports
   * ✅ 1. should return a list of reports for authenticated user
   * ✅ 2. should return empty list when no reports exist
   * ✅ 3. should return 401 when no auth token provided
   * ✅ 4. should handle database errors
   *
   * POST /api/v1/reports
   * ✅ 5. should create a single report successfully
   * ✅ 6. should create multiple reports successfully
   * ✅ 7. should replace all reports when replace=true
   * ✅ 8. should replace with a single report when replace=true
   * ✅ 9. should return 401 when no auth token provided
   * ✅ 10. should handle database errors during save
   * ✅ 11. should handle report not found after save
   * ✅ 12. should handle report not found after save with replace=true
   *
   * PATCH /api/v1/reports/:id
   * ✅ 13. should update a report for authenticated user
   * ✅ 14. should return 401 if no auth token provided
   * ✅ 15. should handle errors when updating a report
   * ✅ 16. should return 404 if report not found for update
   *
   * DELETE /api/v1/reports/:id
   * ✅ 17. should delete a report for authenticated user
   * ✅ 18. should return 401 if no auth token provided
   * ✅ 19. should handle errors when deleting a report
   * ✅ 20. should return 404 if report not found for deletion
   */

  describe('GET /api/v1/reports', () => {
    it('should return a list of reports for authenticated user', async () => {
      const mockReports = [
        {
          id: '1',
          uid: testUserId,
          year: 2023,
          month: 1,
          holdings: [
            {
              id: 'holding-1',
              symbol: 'AAPL',
              count: 10,
              price: 150.5,
              currency: 1,
              dividend: 0.5,
              dividendCurrency: 1,
              dividends: 5,
              position: 1,
            },
          ],
          isLocked: false,
        },
        {
          id: '2',
          uid: testUserId,
          year: 2023,
          month: 2,
          holdings: [
            {
              id: 'holding-2',
              symbol: 'MSFT',
              count: 5,
              price: 300.75,
              currency: 1,
              dividend: 0.6,
              dividendCurrency: 1,
              dividends: 3,
              position: 2,
            },
          ],
          isLocked: false,
        },
      ];

      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('SELECT * FROM users')) {
          cb(null, [{ id: testUserId, jwt: authToken }]);
        } else if (query.includes('SELECT * FROM reports')) {
          cb(null, mockReports);
        }
      });

      const response = await request(app).get('/api/v1/reports').set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.data).toEqual(mockReports);
      expect(db.query).toHaveBeenCalledTimes(2);
      expect(db.query).toHaveBeenNthCalledWith(
        1,
        'SELECT * FROM users WHERE `id`=? AND `jwt`=? LIMIT 1;',
        [testUserId, authToken],
        expect.any(Function),
      );
    });

    it('should return empty list when no reports exist', async () => {
      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('SELECT * FROM users')) {
          cb(null, [{ id: testUserId, jwt: authToken }]);
        } else if (query.includes('SELECT * FROM reports')) {
          cb(null, []); // Return empty array for reports
        }
      });

      const response = await request(app).get('/api/v1/reports').set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.data).toEqual([]);
      expect(db.query).toHaveBeenCalledTimes(2);
      expect(db.query).toHaveBeenNthCalledWith(
        1,
        'SELECT * FROM users WHERE `id`=? AND `jwt`=? LIMIT 1;',
        [testUserId, authToken],
        expect.any(Function),
      );
      expect(db.query).toHaveBeenNthCalledWith(
        2,
        expect.stringContaining('SELECT * FROM reports'),
        expect.arrayContaining([testUserId]),
        expect.any(Function),
      );
    });

    it('should return 401 when no auth token provided', async () => {
      const response = await request(app).get('/api/v1/reports');

      expect(response.status).toBe(401);
      expect(response.body.error.code).toBe(ERROR_AUTH.TOKEN);
      expect(db.query).not.toHaveBeenCalled();
    });

    it('should handle database errors', async () => {
      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('SELECT * FROM users')) {
          cb(null, [{ id: testUserId, jwt: authToken }]);
        } else if (query.includes('SELECT * FROM reports')) {
          cb(new Error('Database connection failed'));
        }
      });

      const response = await request(app).get('/api/v1/reports').set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(500);
      expect(response.body.error.code).toBe(ERROR_REPORT.FETCH);
      expect(response.body.error.message).toBe('Database connection failed');
      expect(db.query).toHaveBeenCalled();
    });
  });

  describe('POST /api/v1/reports', () => {
    it('should create a single report successfully', async () => {
      const expectedId = 'test-report-uuid';
      mockUuid.setValue(expectedId);

      const reportData = {
        year: 2023,
        month: 3,
        holdings: [
          {
            id: 'holding-1',
            symbol: 'AAPL',
            count: 10,
            price: 150.5,
            currency: 1,
            dividend: 0.5,
            dividendCurrency: 1,
            dividends: 5,
            position: 1,
          },
        ],
        isLocked: false,
      };

      const createdReport = {
        ...reportData,
        id: expectedId,
        uid: testUserId,
      };

      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('SELECT * FROM users')) {
          cb(null, [{ id: testUserId, jwt: authToken }]);
        } else if (query.includes('INSERT INTO reports')) {
          cb(null, { insertId: expectedId });
        } else if (query.includes('SELECT * FROM reports')) {
          cb(null, [createdReport]);
        }
      });

      const response = await request(app).post('/api/v1/reports').set('Authorization', `Bearer ${authToken}`).send(reportData);

      expect(response.status).toBe(201);
      expect(response.body.data).toEqual(createdReport);
      expect(db.query).toHaveBeenCalledTimes(3); // Auth check, insert, and select to return the new report
      expect(db.query).toHaveBeenCalledWith(
        expect.stringContaining('INSERT INTO reports'),
        expect.arrayContaining([expectedId, testUserId]),
        expect.any(Function),
      );
    });

    it('should create multiple reports successfully', async () => {
      const expectedIds = ['report-uuid-1', 'report-uuid-2'];
      let currentIdIndex = 0;
      mockUuid.setValue(expectedIds[0]); // Start with first UUID

      const reportsData = [
        {
          year: 2023,
          month: 1,
          holdings: [
            {
              id: 'holding-1',
              symbol: 'AAPL',
              count: 10,
              price: 150.5,
              currency: 1,
              dividend: 0.5,
              dividendCurrency: 1,
              dividends: 5,
              position: 1,
            },
          ],
          isLocked: false,
        },
        {
          year: 2023,
          month: 2,
          holdings: [
            {
              id: 'holding-2',
              symbol: 'MSFT',
              count: 5,
              price: 300.75,
              currency: 1,
              dividend: 0.6,
              dividendCurrency: 1,
              dividends: 3,
              position: 1,
            },
          ],
          isLocked: false,
        },
      ];

      const createdReports = reportsData.map((data, index) => ({
        ...data,
        id: expectedIds[index],
        uid: testUserId,
      }));

      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('SELECT * FROM users')) {
          cb(null, [{ id: testUserId, jwt: authToken }]);
        } else if (query.includes('INSERT INTO reports')) {
          // Update UUID for next insert
          const currentId = expectedIds[currentIdIndex];
          currentIdIndex++;
          if (currentIdIndex < expectedIds.length) {
            mockUuid.setValue(expectedIds[currentIdIndex]);
          }
          cb(null, { insertId: currentId });
        } else if (query.includes('SELECT * FROM reports')) {
          cb(null, createdReports);
        }
      });

      const response = await request(app).post('/api/v1/reports').set('Authorization', `Bearer ${authToken}`).send(reportsData);

      expect(response.status).toBe(201);
      expect(response.body.data).toEqual(createdReports);
      expect(db.query).toHaveBeenCalledWith(
        expect.stringContaining('INSERT INTO reports'),
        expect.arrayContaining([expectedIds[0], testUserId]),
        expect.any(Function),
      );
      expect(db.query).toHaveBeenCalledWith(
        expect.stringContaining('SELECT * FROM reports'),
        expect.arrayContaining([testUserId]),
        expect.any(Function),
      );
    });

    it('should replace all reports when replace=true', async () => {
      const expectedIds = ['replacement-uuid-1', 'replacement-uuid-2'];
      const currentIdIndex = 0;
      mockUuid.setValue(expectedIds[0]); // Start with first UUID

      const reportsData = [
        {
          year: 2023,
          month: 5,
          holdings: [
            {
              id: 'holding-5',
              symbol: 'NVDA',
              count: 20,
              price: 400.5,
              currency: 1,
              dividend: 0.1,
              dividendCurrency: 1,
              dividends: 2,
              position: 1,
            },
          ],
          isLocked: false,
        },
        {
          year: 2023,
          month: 6,
          holdings: [
            {
              id: 'holding-6',
              symbol: 'AMD',
              count: 50,
              price: 110.25,
              currency: 1,
              dividend: 0.0,
              dividendCurrency: 1,
              dividends: 0,
              position: 1,
            },
          ],
          isLocked: false,
        },
      ];

      const createdReports = reportsData.map((data, index) => ({
        ...data,
        id: expectedIds[index],
        uid: testUserId,
      }));

      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('SELECT * FROM users')) {
          cb(null, [{ id: testUserId, jwt: authToken }]);
        } else if (query.includes('DELETE FROM reports')) {
          cb(null, { affectedRows: 3 }); // Simulates 3 reports were deleted
        } else if (query.includes('INSERT INTO reports')) {
          // Don't modify UUID here, just handle the insert
          cb(null, { insertId: expectedIds[currentIdIndex] });
        } else if (query.includes('SELECT * FROM reports')) {
          cb(null, createdReports);
        }
      });

      const response = await request(app)
        .post('/api/v1/reports')
        .query({ replace: 'true' })
        .set('Authorization', `Bearer ${authToken}`)
        .send(reportsData);

      expect(response.status).toBe(200);
      expect(response.body.data).toEqual(createdReports);

      // Verify DELETE was called before INSERT
      expect(db.query).toHaveBeenCalledWith(
        expect.stringContaining('DELETE FROM reports'),
        expect.arrayContaining([testUserId]),
        expect.any(Function),
      );

      // We don't need to verify the exact parameters of INSERT since they're complex
      // and may depend on implementation details. Instead, just verify it was called.
      expect(db.query).toHaveBeenCalledWith(expect.stringContaining('INSERT INTO reports'), expect.any(Array), expect.any(Function));

      // Verify the final SELECT was called to return the new reports
      expect(db.query).toHaveBeenCalledWith(
        expect.stringContaining('SELECT * FROM reports'),
        expect.arrayContaining([testUserId]),
        expect.any(Function),
      );
    });

    it('should replace with a single report when replace=true', async () => {
      const expectedId = 'single-replacement-uuid';
      mockUuid.setValue(expectedId);

      const reportData = {
        year: 2023,
        month: 4,
        holdings: [
          {
            id: 'holding-4',
            symbol: 'GOOGL',
            count: 15,
            price: 125.75,
            currency: 1,
            dividend: 0.0,
            dividendCurrency: 1,
            dividends: 0,
            position: 1,
          },
        ],
        isLocked: false,
      };

      const createdReport = {
        ...reportData,
        id: expectedId,
        uid: testUserId,
      };

      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('SELECT * FROM users')) {
          cb(null, [{ id: testUserId, jwt: authToken }]);
        } else if (query.includes('DELETE FROM reports')) {
          cb(null, { affectedRows: 2 }); // Simulates 2 reports were deleted
        } else if (query.includes('INSERT INTO reports')) {
          cb(null, { insertId: expectedId });
        } else if (query.includes('SELECT * FROM reports')) {
          cb(null, [createdReport]);
        }
      });

      const response = await request(app)
        .post('/api/v1/reports')
        .query({ replace: 'true' })
        .set('Authorization', `Bearer ${authToken}`)
        .send(reportData);

      expect(response.status).toBe(200);
      expect(response.body.data).toEqual(createdReport);

      // Verify DELETE was called before INSERT
      expect(db.query).toHaveBeenCalledWith(
        expect.stringContaining('DELETE FROM reports'),
        expect.arrayContaining([testUserId]),
        expect.any(Function),
      );

      // Verify INSERT was called after DELETE
      expect(db.query).toHaveBeenCalledWith(expect.stringContaining('INSERT INTO reports'), expect.any(Array), expect.any(Function));

      // Verify the final SELECT was called to return the new report
      expect(db.query).toHaveBeenCalledWith(
        expect.stringContaining('SELECT * FROM reports'),
        expect.arrayContaining([testUserId]),
        expect.any(Function),
      );
    });

    it('should return 401 when no auth token provided', async () => {
      const reportData = {
        year: 2023,
        month: 7,
        holdings: [
          {
            id: 'holding-7',
            symbol: 'TSLA',
            count: 10,
            price: 250.75,
            currency: 1,
            dividend: 0.0,
            dividendCurrency: 1,
            dividends: 0,
            position: 1,
          },
        ],
        isLocked: false,
      };

      const response = await request(app).post('/api/v1/reports').send(reportData);

      expect(response.status).toBe(401);
      expect(response.body.error.code).toBe(ERROR_AUTH.TOKEN);
      expect(db.query).not.toHaveBeenCalled();
    });

    it('should handle database errors during save', async () => {
      // Set the UUID explicitly
      const expectedId = 'error-test-uuid';
      mockUuid.setValue(expectedId);

      // Create a single report (not an array)
      const reportData = {
        year: 2023,
        month: 8,
        holdings: [
          {
            id: 'holding-8',
            symbol: 'AMZN',
            count: 8,
            price: 140.25,
            currency: 1,
            dividend: 0.0,
            dividendCurrency: 1,
            dividends: 0,
            position: 1,
          },
        ],
        isLocked: false,
      };

      // Mock implementation with synchronous error
      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('SELECT * FROM users')) {
          cb(null, [{ id: testUserId, jwt: authToken }]);
        } else if (query.includes('INSERT INTO reports')) {
          // Simulate database error with synchronous callback
          const error = new Error('Database connection failed during save');
          cb(error);
        }
      });

      const response = await request(app).post('/api/v1/reports').set('Authorization', `Bearer ${authToken}`).send(reportData);

      expect(response.status).toBe(500);
      expect(response.body.error.code).toBe(ERROR_REPORT.CREATE);
      expect(response.body.error.message).toBe('Database connection failed during save');
    });

    it('should handle report not found after save', async () => {
      const expectedId = 'missing-report-uuid';
      mockUuid.setValue(expectedId);

      const reportData = {
        year: 2023,
        month: 9,
        holdings: [
          {
            id: 'holding-9',
            symbol: 'TSLA',
            count: 12,
            price: 225.5,
            currency: 1,
            dividend: 0.0,
            dividendCurrency: 1,
            dividends: 0,
            position: 1,
          },
        ],
        isLocked: false,
      };

      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('SELECT * FROM users')) {
          cb(null, [{ id: testUserId, jwt: authToken }]);
        } else if (query.includes('INSERT INTO reports')) {
          // Successful insert
          cb(null, { insertId: expectedId });
        } else if (query.includes('SELECT * FROM reports WHERE `id`')) {
          // Report not found after insert - return empty array
          cb(null, []);
        }
      });

      const response = await request(app).post('/api/v1/reports').set('Authorization', `Bearer ${authToken}`).send(reportData);

      expect(response.status).toBe(500);
      expect(response.body.error.code).toBe(ERROR_REPORT.CREATE);
      expect(response.body.error.message).toBe('Report save operation failed silently');
    });

    it('should handle report not found after save with replace=true', async () => {
      const expectedId = 'missing-replacement-report-uuid';
      mockUuid.setValue(expectedId);

      const reportData = {
        year: 2023,
        month: 10,
        holdings: [
          {
            id: 'holding-10',
            symbol: 'META',
            count: 30,
            price: 310.75,
            currency: 1,
            dividend: 0.0,
            dividendCurrency: 1,
            dividends: 0,
            position: 1,
          },
        ],
        isLocked: false,
      };

      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('SELECT * FROM users')) {
          cb(null, [{ id: testUserId, jwt: authToken }]);
        } else if (query.includes('DELETE FROM reports')) {
          cb(null, { affectedRows: 2 }); // Successfully deleted existing reports
        } else if (query.includes('INSERT INTO reports')) {
          // Successful insert
          cb(null, { insertId: expectedId });
        } else if (query.includes('SELECT * FROM reports WHERE `id`')) {
          // Report not found after insert - return empty array
          cb(null, []);
        }
      });

      const response = await request(app)
        .post('/api/v1/reports')
        .query({ replace: 'true' })
        .set('Authorization', `Bearer ${authToken}`)
        .send(reportData);

      expect(response.status).toBe(500);
      expect(response.body.error.code).toBe(ERROR_REPORT.CREATE);
      expect(response.body.error.message).toBe('Report save operation failed silently');
    });
  });

  describe('PATCH /api/v1/reports/:id', () => {
    it('should update a report for authenticated user', async () => {
      const reportId = 'test-report-id';
      const originalReport = {
        id: reportId,
        uid: testUserId,
        year: 2023,
        month: 1,
        holdings: [
          {
            id: 'holding-1',
            symbol: 'AAPL',
            count: 10,
            price: 150.5,
            currency: 1,
            dividend: 0.5,
            dividendCurrency: 1,
            dividends: 5,
            position: 1,
          },
        ],
        isLocked: false,
      };

      const updateData = {
        year: 2023,
        month: 2,
        holdings: [
          {
            id: 'holding-1',
            symbol: 'AAPL',
            count: 15, // Updated count
            price: 160.75, // Updated price
            currency: 1,
            dividend: 0.5,
            dividendCurrency: 1,
            dividends: 7.5, // Updated dividends
            position: 1,
          },
        ],
        isLocked: true, // Updated lock status
      };

      const updatedReport = {
        ...updateData,
        id: reportId,
        uid: testUserId,
      };

      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('SELECT * FROM users')) {
          cb(null, [{ id: testUserId, jwt: authToken }]);
        } else if (query.includes('SELECT * FROM reports WHERE id =')) {
          // First return original report, then return updated report
          cb(null, [originalReport]);
        } else if (query.includes('UPDATE reports')) {
          cb(null, { affectedRows: 1 });
        } else if (query.includes('SELECT * FROM reports WHERE `id`')) {
          cb(null, [updatedReport]);
        }
      });

      const response = await request(app).patch(`/api/v1/reports/${reportId}`).set('Authorization', `Bearer ${authToken}`).send(updateData);

      expect(response.status).toBe(200);
      expect(response.body.data).toEqual(updatedReport);
      expect(db.query).toHaveBeenCalledWith(
        expect.stringContaining('UPDATE reports'),
        expect.arrayContaining([reportId, testUserId]),
        expect.any(Function),
      );
    });

    it('should return 401 if no auth token provided', async () => {
      const reportId = 'test-report-id';
      const updateData = {
        year: 2023,
        month: 2,
        holdings: [
          {
            id: 'holding-1',
            symbol: 'AAPL',
            count: 15,
            price: 160.75,
            currency: 1,
            dividend: 0.5,
            dividendCurrency: 1,
            dividends: 7.5,
            position: 1,
          },
        ],
        isLocked: true,
      };

      const response = await request(app).patch(`/api/v1/reports/${reportId}`).send(updateData);

      expect(response.status).toBe(401);
      expect(response.body.error.code).toBe(ERROR_AUTH.TOKEN);
      expect(db.query).not.toHaveBeenCalled();
    });

    it('should handle errors when updating a report', async () => {
      const reportId = 'test-report-id';
      const originalReport = {
        id: reportId,
        uid: testUserId,
        year: 2023,
        month: 1,
        holdings: [
          {
            id: 'holding-1',
            symbol: 'AAPL',
            count: 10,
            price: 150.5,
            currency: 1,
            dividend: 0.5,
            dividendCurrency: 1,
            dividends: 5,
            position: 1,
          },
        ],
        isLocked: false,
      };

      const updateData = {
        year: 2023,
        month: 2,
        holdings: [
          {
            id: 'holding-1',
            symbol: 'AAPL',
            count: 15,
            price: 160.75,
            currency: 1,
            dividend: 0.5,
            dividendCurrency: 1,
            dividends: 7.5,
            position: 1,
          },
        ],
        isLocked: true,
      };

      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('SELECT * FROM users')) {
          cb(null, [{ id: testUserId, jwt: authToken }]);
        } else if (query.includes('SELECT * FROM reports WHERE `id`')) {
          cb(null, [originalReport]);
        } else if (query.includes('UPDATE reports')) {
          cb(new Error('Database connection failed during update'));
        }
      });

      const response = await request(app).patch(`/api/v1/reports/${reportId}`).set('Authorization', `Bearer ${authToken}`).send(updateData);

      expect(response.status).toBe(500);
      expect(response.body.error.code).toBe(ERROR_REPORT.UPDATE);
      expect(response.body.error.message).toBe('Database connection failed during update');
    });

    it('should return 404 if report not found for update', async () => {
      const reportId = 'non-existent-report-id';
      const updateData = {
        year: 2023,
        month: 2,
        holdings: [
          {
            id: 'holding-1',
            symbol: 'AAPL',
            count: 15,
            price: 160.75,
            currency: 1,
            dividend: 0.5,
            dividendCurrency: 1,
            dividends: 7.5,
            position: 1,
          },
        ],
        isLocked: true,
      };

      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('SELECT * FROM users')) {
          cb(null, [{ id: testUserId, jwt: authToken }]);
        } else if (query.includes('SELECT * FROM reports')) {
          // Return empty array to simulate report not found
          cb(null, []);
        }
      });

      const response = await request(app).patch(`/api/v1/reports/${reportId}`).set('Authorization', `Bearer ${authToken}`).send(updateData);

      expect(response.status).toBe(404);
      expect(response.body.error.code).toBe(ERROR_REPORT.UPDATE);
      expect(response.body.error.message).toBe('Report not found for update');
    });
  });

  describe('DELETE /api/v1/reports/:id', () => {
    it('should delete a report for authenticated user', async () => {
      const reportId = 'test-report-id';

      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('SELECT * FROM users')) {
          cb(null, [{ id: testUserId, jwt: authToken }]);
        } else if (query.includes('DELETE FROM reports')) {
          cb(null, { affectedRows: 1 });
        }
      });

      const response = await request(app).delete(`/api/v1/reports/${reportId}`).set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(204);
      expect(response.body).toEqual({});
      expect(db.query).toHaveBeenCalledWith(
        expect.stringContaining('DELETE FROM reports'),
        expect.arrayContaining([reportId, testUserId]),
        expect.any(Function),
      );
    });

    it('should return 401 if no auth token provided', async () => {
      const reportId = 'test-report-id';

      const response = await request(app).delete(`/api/v1/reports/${reportId}`);

      expect(response.status).toBe(401);
      expect(response.body.error.code).toBe(ERROR_AUTH.TOKEN);
      expect(db.query).not.toHaveBeenCalled();
    });

    it('should handle errors when deleting a report', async () => {
      const reportId = 'test-report-id';

      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('SELECT * FROM users')) {
          cb(null, [{ id: testUserId, jwt: authToken }]);
        } else if (query.includes('DELETE FROM reports')) {
          cb(new Error('Database connection failed during delete'));
        }
      });

      const response = await request(app).delete(`/api/v1/reports/${reportId}`).set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(500);
      expect(response.body.error.code).toBe(ERROR_REPORT.DELETE);
      expect(response.body.error.message).toBe('Database connection failed during delete');
    });

    it('should return 404 if report not found for deletion', async () => {
      const reportId = 'non-existent-report-id';

      // @ts-expect-error - mockImplementation is jest fn not db query
      db.query.mockImplementation((query, params, cb) => {
        if (query.includes('SELECT * FROM users')) {
          cb(null, [{ id: testUserId, jwt: authToken }]);
        } else if (query.includes('DELETE FROM reports')) {
          cb(null, { affectedRows: 0 }); // No rows affected means report wasn't found
        }
      });

      const response = await request(app).delete(`/api/v1/reports/${reportId}`).set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(404);
      expect(response.body.error.code).toBe(ERROR_REPORT.DELETE);
      expect(response.body.error.message).toBe('Report not found for deletion');
    });
  });
});
