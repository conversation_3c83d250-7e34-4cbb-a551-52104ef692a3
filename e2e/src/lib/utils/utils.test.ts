import { round, uniq, sort<PERSON><PERSON><PERSON><PERSON>, base64<PERSON><PERSON>de, base64Decode, isEmpty, toJson } from '@api/lib/utils';

describe('Lib | Utils', () => {
  /**
   * round
   */
  describe('round', () => {
    it('should round values to 2nd decimal', () => {
      expect(round(11.256)).toBe(11.26);
      expect(round(110.31618465132)).toBe(110.32);
      expect(round(110.3199)).toBe(110.32);
    });
  });

  /**
   * uniq
   */
  describe('uniq', () => {
    const arrayValues = ['apple', 'banana', 'apple', 'mango'];
    const arrayObjects = [{ fruit: 'apple' }, { fruit: 'banana' }, { fruit: 'apple' }, { fruit: 'mango' }];

    it('should extract uniq values from a regular array', () => {
      expect(uniq(arrayValues)).toEqual(['apple', 'banana', 'mango']);
      expect(uniq(arrayObjects, 'fruit')).toEqual(['apple', 'banana', 'mango']);
    });
  });

  /**
   * sortByKey
   */
  describe('sortByKey', () => {
    const arrayObjects = [
      { name: 'apple', position: 2 },
      { name: 'mango', position: 3 },
      { name: 'banana', position: 1 }
    ];
    const arrayObjects2 = [
      { name: 'banana', position: 1 },
      { name: 'apple', position: 1 },
      { name: 'mango', position: 3 }
    ];

    it('should sort array by "position" key', () => {
      expect(arrayObjects.sort(sortByKey('position'))).toEqual([
        { name: 'banana', position: 1 },
        { name: 'apple', position: 2 },
        { name: 'mango', position: 3 }
      ]);
    });

    it('should try to sort with same values', () => {
      expect(arrayObjects2.sort(sortByKey('position'))).toEqual([
        { name: 'banana', position: 1 },
        { name: 'apple', position: 1 },
        { name: 'mango', position: 3 }
      ]);
    });

    it('should sort array by "name" key', () => {
      expect(arrayObjects.sort(sortByKey('name'))).toEqual([
        { name: 'apple', position: 2 },
        { name: 'banana', position: 1 },
        { name: 'mango', position: 3 }
      ]);
    });

    it('should sort an already sorted array without errors', () => {
      const array = [
        { name: 'banana', position: 1 },
        { name: 'apple', position: 2 },
        { name: 'mango', position: 3 }
      ];

      expect(array.sort(sortByKey('position'))).toEqual(array);
    });
  });

  const encode = (data: DynamicObject | DynamicObject[] | string[]) => Buffer.from(JSON.stringify(data)).toString('base64');

  /**
   * base64Encode
   */
  describe('base64Encode', () => {
    const arrayValues = ['apple', 'banana'];
    const arrayObjects = [{ fruit: 'apple' }, { fruit: 'banana' }];
    const object = { fruit: 'apple' };

    it('should encode an array into base64 string', () => {
      expect(base64Encode(arrayValues)).toEqual(encode(arrayValues));
    });

    it('should encode an array of objects into base64 string', () => {
      expect(base64Encode(arrayObjects)).toEqual(encode(arrayObjects));
    });

    it('should encode an object into base64 string', () => {
      expect(base64Encode(object)).toEqual(encode(object));
    });
  });

  /**
   * base64Decode
   */
  describe('base64Decode', () => {
    const data = [{ a: 'a' }, { b: 'b' }];
    const encodedString = encode(data);

    it('should decode base64 string back into array of objects', () => {
      expect(base64Decode(encodedString)).toEqual(data);
    });
  });

  /**
   * isEmpty
   * export const isEmpty = (val) => val == null || val == '' || (Array.isArray(val) && val.length === 0);
   */
  describe('isEmpty', () => {
    it('should correctly check undefined and null', () => {
      expect(isEmpty(undefined)).toEqual(true);
      expect(isEmpty(null)).toEqual(true);
    });
    it('should correctly check empty string', () => {
      expect(isEmpty('')).toEqual(true);
      expect(isEmpty(' ')).toEqual(false);
    });
    it('should correctly check empty array', () => {
      expect(isEmpty([])).toEqual(true);
      expect(isEmpty([1])).toEqual(false);
    });
  });

  /**
   * toJson
   */
  describe('toJson', () => {
    it('should throw error', () => {
      try {
        // @ts-expect-error - expected
        toJson();
      } catch (err: unknown) {
        const { message } = err as Error;
        expect(message).toEqual('Object serializer is missing data or it is not an object.');
      }
    });

    it('should convert to json object', () => {
      expect(toJson({ a: 1, b: () => { /* intentionally empty */ } })).toEqual({ a: 1 });
    });

    it('should convert to json object with hidden props', () => {
      expect(toJson({ a: 1, b: () => { /* intentionally empty */ }, c: 2 }, { hidden: ['c'] })).toEqual({ a: 1 });
    });
  });
});
