import {
  isLastDayOfMonth,
  areUSAExchangesClosed,
  getMonthEndDateString,
  shouldSaveMonthlyRates,
  logMarketCheckResults,
} from '@api/lib/utils/market-utils';

describe('Market Utils', () => {
  describe('isLastDayOfMonth', () => {
    it('should return true for the last day of February in a leap year', () => {
      const date = new Date('2024-02-29T12:00:00Z'); // Leap year
      expect(isLastDayOfMonth(date)).toBe(true);
    });

    it('should return true for the last day of February in a non-leap year', () => {
      const date = new Date('2023-02-28T12:00:00Z'); // Non-leap year
      expect(isLastDayOfMonth(date)).toBe(true);
    });

    it('should return true for the last day of January', () => {
      const date = new Date('2024-01-31T12:00:00Z');
      expect(isLastDayOfMonth(date)).toBe(true);
    });

    it('should return false for the first day of the month', () => {
      const date = new Date('2024-02-01T12:00:00Z');
      expect(isLastDayOfMonth(date)).toBe(false);
    });

    it('should return false for a middle day of the month', () => {
      const date = new Date('2024-02-15T12:00:00Z');
      expect(isLastDayOfMonth(date)).toBe(false);
    });
  });

  describe('areUSAExchangesClosed', () => {
    it('should return true on Saturday', () => {
      const date = new Date('2024-02-24T14:00:00Z'); // Saturday
      expect(areUSAExchangesClosed(date)).toBe(true);
    });

    it('should return true on Sunday', () => {
      const date = new Date('2024-02-25T14:00:00Z'); // Sunday
      expect(areUSAExchangesClosed(date)).toBe(true);
    });

    it('should return true before market opens (9:30 AM ET)', () => {
      // 9:00 AM ET on a weekday
      const date = new Date('2024-02-26T14:00:00Z'); // Monday 9:00 AM ET (14:00 UTC)
      expect(areUSAExchangesClosed(date)).toBe(true);
    });

    it('should return false during market hours', () => {
      // 2:00 PM ET on a weekday (market is open 9:30 AM - 4:00 PM ET)
      const date = new Date('2024-02-26T19:00:00Z'); // Monday 2:00 PM ET (19:00 UTC)
      expect(areUSAExchangesClosed(date)).toBe(false);
    });

    it('should return true after market closes (4:00 PM ET)', () => {
      // 5:00 PM ET on a weekday
      const date = new Date('2024-02-26T22:00:00Z'); // Monday 5:00 PM ET (22:00 UTC)
      expect(areUSAExchangesClosed(date)).toBe(true);
    });
  });

  describe('getMonthEndDateString', () => {
    it('should return correct month end date for February in leap year', () => {
      const date = new Date('2024-02-15T12:00:00Z');
      expect(getMonthEndDateString(date)).toBe('2024-02-29');
    });

    it('should return correct month end date for February in non-leap year', () => {
      const date = new Date('2023-02-15T12:00:00Z');
      expect(getMonthEndDateString(date)).toBe('2023-02-28');
    });

    it('should return correct month end date for January', () => {
      const date = new Date('2024-01-15T12:00:00Z');
      expect(getMonthEndDateString(date)).toBe('2024-01-31');
    });

    it('should return correct month end date for April (30 days)', () => {
      const date = new Date('2024-04-15T12:00:00Z');
      expect(getMonthEndDateString(date)).toBe('2024-04-30');
    });
  });

  describe('shouldSaveMonthlyRates', () => {
    it('should return shouldSave true when it is last day of month and exchanges are closed', () => {
      // Last day of February (leap year) at 6 PM ET (after market close)
      const date = new Date('2024-02-29T23:00:00Z'); // 6:00 PM ET
      const result = shouldSaveMonthlyRates(date);

      expect(result.isLastDayOfMonth).toBe(true);
      expect(result.areExchangesClosed).toBe(true);
      expect(result.shouldSave).toBe(true);
      expect(result.monthEndDate).toBe('2024-02-29');
    });

    it('should return shouldSave false when it is last day of month but exchanges are open', () => {
      // Last day of February (leap year) at 2 PM ET (during market hours)
      const date = new Date('2024-02-29T19:00:00Z'); // 2:00 PM ET
      const result = shouldSaveMonthlyRates(date);

      expect(result.isLastDayOfMonth).toBe(true);
      expect(result.areExchangesClosed).toBe(false);
      expect(result.shouldSave).toBe(false);
      expect(result.monthEndDate).toBe('2024-02-29');
    });

    it('should return shouldSave false when exchanges are closed but it is not last day of month', () => {
      // Middle of February at 6 PM ET (after market close)
      const date = new Date('2024-02-15T23:00:00Z'); // 6:00 PM ET
      const result = shouldSaveMonthlyRates(date);

      expect(result.isLastDayOfMonth).toBe(false);
      expect(result.areExchangesClosed).toBe(true);
      expect(result.shouldSave).toBe(false);
      expect(result.monthEndDate).toBe('2024-02-29');
    });

    it('should include checkTime in ISO format', () => {
      const date = new Date('2024-02-29T23:00:00Z');
      const result = shouldSaveMonthlyRates(date);

      expect(result.checkTime).toBe('2024-02-29T23:00:00.000Z');
    });
  });

  describe('logMarketCheckResults', () => {
    let consoleSpy: jest.SpyInstance;

    beforeEach(() => {
      consoleSpy = jest.spyOn(console, 'log').mockImplementation(() => {
        return undefined;
      });
    });

    afterEach(() => {
      consoleSpy.mockRestore();
    });

    it('should log all market check results when shouldSave is true', () => {
      const mockCheckResult = {
        isLastDayOfMonth: true,
        areExchangesClosed: true,
        shouldSave: true,
        monthEndDate: '2024-02-29',
        checkTime: '2024-02-29T23:00:00.000Z',
      };

      logMarketCheckResults(mockCheckResult);

      expect(consoleSpy).toHaveBeenCalledTimes(7);
      expect(consoleSpy).toHaveBeenNthCalledWith(1, '=== Monthly Currency Rates Check ===');
      expect(consoleSpy).toHaveBeenNthCalledWith(2, 'Check Time: 2024-02-29T23:00:00.000Z');
      expect(consoleSpy).toHaveBeenNthCalledWith(3, 'Is Last Day of Month: true');
      expect(consoleSpy).toHaveBeenNthCalledWith(4, 'Are USA Exchanges Closed: true');
      expect(consoleSpy).toHaveBeenNthCalledWith(5, 'Month End Date: 2024-02-29');
      expect(consoleSpy).toHaveBeenNthCalledWith(6, 'Should Save Monthly Rates: true');
      expect(consoleSpy).toHaveBeenNthCalledWith(7, '=====================================');
    });

    it('should log all market check results when shouldSave is false', () => {
      const mockCheckResult = {
        isLastDayOfMonth: false,
        areExchangesClosed: true,
        shouldSave: false,
        monthEndDate: '2024-02-29',
        checkTime: '2024-02-15T19:00:00.000Z',
      };

      logMarketCheckResults(mockCheckResult);

      expect(consoleSpy).toHaveBeenCalledTimes(7);
      expect(consoleSpy).toHaveBeenNthCalledWith(1, '=== Monthly Currency Rates Check ===');
      expect(consoleSpy).toHaveBeenNthCalledWith(2, 'Check Time: 2024-02-15T19:00:00.000Z');
      expect(consoleSpy).toHaveBeenNthCalledWith(3, 'Is Last Day of Month: false');
      expect(consoleSpy).toHaveBeenNthCalledWith(4, 'Are USA Exchanges Closed: true');
      expect(consoleSpy).toHaveBeenNthCalledWith(5, 'Month End Date: 2024-02-29');
      expect(consoleSpy).toHaveBeenNthCalledWith(6, 'Should Save Monthly Rates: false');
      expect(consoleSpy).toHaveBeenNthCalledWith(7, '=====================================');
    });

    it('should log market check results with mixed conditions', () => {
      const mockCheckResult = {
        isLastDayOfMonth: true,
        areExchangesClosed: false,
        shouldSave: false,
        monthEndDate: '2024-01-31',
        checkTime: '2024-01-31T19:00:00.000Z',
      };

      logMarketCheckResults(mockCheckResult);

      expect(consoleSpy).toHaveBeenCalledTimes(7);
      expect(consoleSpy).toHaveBeenNthCalledWith(1, '=== Monthly Currency Rates Check ===');
      expect(consoleSpy).toHaveBeenNthCalledWith(2, 'Check Time: 2024-01-31T19:00:00.000Z');
      expect(consoleSpy).toHaveBeenNthCalledWith(3, 'Is Last Day of Month: true');
      expect(consoleSpy).toHaveBeenNthCalledWith(4, 'Are USA Exchanges Closed: false');
      expect(consoleSpy).toHaveBeenNthCalledWith(5, 'Month End Date: 2024-01-31');
      expect(consoleSpy).toHaveBeenNthCalledWith(6, 'Should Save Monthly Rates: false');
      expect(consoleSpy).toHaveBeenNthCalledWith(7, '=====================================');
    });

    it('should work with result from shouldSaveMonthlyRates function', () => {
      // Test integration with actual shouldSaveMonthlyRates function
      const date = new Date('2024-02-29T23:00:00Z'); // Last day of month, after market close
      const checkResult = shouldSaveMonthlyRates(date);

      logMarketCheckResults(checkResult);

      expect(consoleSpy).toHaveBeenCalledTimes(7);
      expect(consoleSpy).toHaveBeenCalledWith('=== Monthly Currency Rates Check ===');
      expect(consoleSpy).toHaveBeenCalledWith('Check Time: 2024-02-29T23:00:00.000Z');
      expect(consoleSpy).toHaveBeenCalledWith('Is Last Day of Month: true');
      expect(consoleSpy).toHaveBeenCalledWith('Are USA Exchanges Closed: true');
      expect(consoleSpy).toHaveBeenCalledWith('Month End Date: 2024-02-29');
      expect(consoleSpy).toHaveBeenCalledWith('Should Save Monthly Rates: true');
      expect(consoleSpy).toHaveBeenCalledWith('=====================================');
    });
  });
});
