import { RESPONSE_TYPE } from '@api/lib/constants/response-types';
import Response from '@api/lib/utils/response';
import ErrorCode from '@api/models/error-code';

describe('Lib | Utils', () => {
  describe('Response', () => {
    it('should assemble error response', () => {
      const error = ErrorCode({ code: 'test' });

      expect(Response(RESPONSE_TYPE.ERROR, error)).toEqual({ error });
    });

    it('should assemble data response', () => {
      const dataA = { a: 'test' };
      const dataB = [dataA];

      expect(Response(RESPONSE_TYPE.DATA, dataA)).toEqual({ data: dataA });
      expect(Response(RESPONSE_TYPE.DATA, dataB)).toEqual({ data: dataB });
    });
  });
});
