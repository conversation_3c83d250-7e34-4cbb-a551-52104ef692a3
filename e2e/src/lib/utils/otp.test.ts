import Otp from '@api/lib/utils/otp';

describe('Lib | Utils', () => {
  describe('Otp', () => {
    it('should generate correct Otp code', () => {
      const defaultOtp = Otp();
      const otp5 = Otp(5);
      const otp8 = Otp(8);

      expect(defaultOtp.length).toEqual(5);
      expect(/^[0-9A-Z]{5}$/.test(defaultOtp)).toBe(true);
      expect(otp5.length).toEqual(5);
      expect(/^[0-9A-Z]{5}$/.test(otp5)).toBe(true);
      expect(otp8.length).toEqual(8);
      expect(/^[0-9A-Z]{8}$/.test(otp8)).toBe(true);
      expect(/^[0-9A-Z]{5}$/.test(otp8)).toBe(false);
    });
  });
});
