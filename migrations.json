{"migrations": [{"version": "21.0.0-beta.8", "description": "Removes the legacy cache configuration from nx.json", "implementation": "./src/migrations/update-21-0-0/remove-legacy-cache", "package": "nx", "name": "remove-legacy-cache"}, {"version": "21.0.0-beta.8", "description": "Removes the legacy cache configuration from nx.json", "implementation": "./src/migrations/update-21-0-0/remove-custom-tasks-runner", "package": "nx", "name": "remove-custom-tasks-runner"}, {"version": "21.0.0-beta.11", "description": "Updates release version config based on the breaking changes in Nx v21", "implementation": "./src/migrations/update-21-0-0/release-version-config-changes", "package": "nx", "name": "release-version-config-changes"}, {"version": "21.0.0-beta.11", "description": "Updates release changelog config based on the breaking changes in Nx v21", "implementation": "./src/migrations/update-21-0-0/release-changelog-config-changes", "package": "nx", "name": "release-changelog-config-changes"}, {"version": "21.1.0-beta.2", "description": "Adds **/nx-rules.mdc and **/nx.instructions.md to .gitignore if not present", "implementation": "./src/migrations/update-21-1-0/add-gitignore-entry", "package": "nx", "name": "21-1-0-add-ignore-entries-for-nx-rule-files"}, {"cli": "nx", "version": "21.0.0-beta.9", "description": "Replace usage of `getJestProjects` with `getJestProjectsAsync`.", "implementation": "./src/migrations/update-21-0-0/replace-getJestProjects-with-getJestProjectsAsync", "package": "@nx/jest", "name": "replace-getJestProjects-with-getJestProjectsAsync-v21"}, {"version": "21.0.0-beta.10", "description": "Remove the previously deprecated and unused `tsConfig` option from the `@nx/jest:jest` executor.", "implementation": "./src/migrations/update-21-0-0/remove-tsconfig-option-from-jest-executor", "package": "@nx/jest", "name": "remove-tsconfig-option-from-jest-executor"}]}