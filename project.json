{"name": "api", "$schema": "node_modules/nx/schemas/project-schema.json", "includedScripts": [], "sourceRoot": "src", "projectType": "application", "tags": [], "targets": {"build": {"executor": "@nx/esbuild:esbuild", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"platform": "node", "outputPath": "dist/api", "format": ["cjs"], "bundle": false, "main": "src/main.ts", "tsConfig": "tsconfig.app.json", "assets": ["src/assets"], "generatePackageJson": true, "esbuildOptions": {"sourcemap": true, "outExtension": {".js": ".js"}}}, "configurations": {"development": {}, "production": {"generateLockfile": true, "esbuildOptions": {"sourcemap": false, "outExtension": {".js": ".js"}}}}}, "serve": {"executor": "@nx/js:node", "defaultConfiguration": "development", "dependsOn": ["build"], "options": {"buildTarget": "api:build", "runBuildTargetDependencies": false}, "configurations": {"development": {"buildTarget": "api:build:development"}, "production": {"buildTarget": "api:build:production"}}}, "test": {"options": {"passWithNoTests": true}}, "docker-build": {"dependsOn": ["build"], "command": "docker build -f Dockerfile . -t api"}}}