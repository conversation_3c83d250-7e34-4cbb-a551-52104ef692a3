export default {
  displayName: 'api',
  preset: './jest.preset.js',
  testEnvironment: 'node',
  transform: {
    '^.+\\.[tj]s$': ['ts-jest', { tsconfig: '<rootDir>/tsconfig.spec.json' }],
  },
  moduleFileExtensions: ['ts', 'js', 'html'],
  coverageDirectory: './coverage',
  testMatch: [
    '<rootDir>/src/**/__tests__/**/*.[jt]s?(x)',
    '<rootDir>/src/**/*(*.)@(spec|test).[jt]s?(x)',
    '<rootDir>/e2e/src/**/*(*.)@(spec|test).[jt]s?(x)',
  ],
  // Coverage configuration
  collectCoverage: true,
  coverageReporters: ['text-summary', 'lcov', 'cobertura', 'json-summary'],
  collectCoverageFrom: [
    'src/**/*.{js,ts}',
    '!**/node_modules/**',
    '!**/vendor/**',
    '!**/dist/**',
    '!**/*.spec.{js,ts}',
    '!src/controllers/index.ts',
    '!src/lib/constants/index.ts',
    '!src/models/index.ts',
    '!src/services/tasks/index.ts',
    '!src/services/index.ts',
    '!src/connection.ts',
    '!src/main.ts',
    '!src/routes.ts',
  ],
  // Exclude dist directory from being scanned
  modulePathIgnorePatterns: [
    '<rootDir>/dist/',
    '<rootDir>/.nx/cache'
  ],
  // No threshold for now
  // coverageThreshold: {
  //   global: {
  //     statements: 50,
  //     branches: 50,
  //     functions: 50,
  //     lines: 50
  //   }
  // }
};
