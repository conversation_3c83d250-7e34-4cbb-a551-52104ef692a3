{"compilerOptions": {"baseUrl": ".", "declaration": false, "emitDecoratorMetadata": true, "esModuleInterop": true, "experimentalDecorators": true, "importHelpers": true, "lib": ["es2020", "dom"], "module": "esnext", "moduleResolution": "node", "paths": {"@/*": ["./*"], "@api/*": ["src/*"], "@e2e/*": ["e2e/src/*"], "@types/*": ["types/*"]}, "pretty": true, "resolveJsonModule": true, "rootDir": ".", "skipDefaultLibCheck": true, "skipLibCheck": true, "sourceMap": true, "target": "es2015"}, "exclude": ["node_modules", "tmp", "dist"], "files": [], "include": [], "references": [{"path": "./tsconfig.app.json"}, {"path": "./tsconfig.spec.json"}]}