{"name": "dt-api", "version": "1.7.0", "private": true, "license": "MIT", "scripts": {"migrate": "node -r @swc-node/register ./node_modules/.bin/db-migrate up", "migrate:create": "node -r @swc-node/register ./node_modules/.bin/db-migrate create", "migrate:rollback": "node -r @swc-node/register ./node_modules/.bin/db-migrate down", "dev": "nx run api:serve:development", "start": "NODE_ENV=production nx run api:serve:production", "test": "nx test api --coverage", "lint": "nx lint api && nx lint e2e"}, "nx": {"includedScripts": []}, "dependencies": {"bcrypt": "^6.0.0", "cheerio": "^1.0.0", "compression": "^1.8.0", "connect-redis": "^8.1.0", "db-migrate": "^0.11.14", "db-migrate-mysql": "^3.0.0", "express": "^5.1.0", "express-session": "^1.18.1", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "mysql2": "^3.14.1", "node-cron": "^3.0.3", "redis": "^5.1.1", "request": "^2.88.2", "request-ip": "^3.3.0", "request-promise": "^4.2.6"}, "devDependencies": {"@eslint/js": "^9.28.0", "@nx/esbuild": "21.1.2", "@nx/eslint": "21.1.2", "@nx/eslint-plugin": "21.1.2", "@nx/jest": "21.1.2", "@nx/js": "21.1.2", "@nx/node": "21.1.2", "@nx/workspace": "21.1.2", "@swc-node/register": "~1.10.10", "@swc/core": "~1.11.29", "@swc/helpers": "~0.5.17", "@types/bcrypt": "^5.0.2", "@types/compression": "^1.8.0", "@types/express": "^5.0.2", "@types/express-session": "^1.18.1", "@types/jest": "^29.5.14", "@types/jsonwebtoken": "^9.0.9", "@types/morgan": "^1.9.9", "@types/node": "~22.15.29", "@types/node-cron": "^3.0.11", "@types/request-ip": "^0.0.41", "@types/request-promise": "^4.1.51", "@types/supertest": "^6.0.3", "esbuild": "^0.25.5", "eslint": "^9.28.0", "eslint-config-prettier": "10.1.5", "jest": "^29.7.0", "jest-environment-node": "^29.7.0", "jest-html-reporter": "^4.1.0", "nx": "21.1.2", "prettier": "^3.5.3", "supertest": "^7.1.1", "ts-jest": "^29.3.4", "ts-node": "10.9.2", "tslib": "^2.8.1", "typescript": "~5.8.3", "typescript-eslint": "^8.33.0"}}