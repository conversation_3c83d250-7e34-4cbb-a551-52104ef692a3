image: node:22

variables:
  GIT_DEPTH: 100

stages:
  - lint-test-build
  - deploy
  - version

# Main job
lint-test-build:
  stage: lint-test-build
  interruptible: true
  only:
    - master
    - merge_requests
  cache:
    key:
      files:
        - yarn.lock
    paths:
      - .npm/
  script:
    # Copy .env.example to .env for CI testing
    - cp .env.example .env

    # Regular CI commands
    - yarn install --frozen-lockfile --cache .npm --prefer-offline
    - NX_HEAD=$CI_COMMIT_SHA
    - NX_BASE=${CI_MERGE_REQUEST_DIFF_BASE_SHA:-$CI_COMMIT_BEFORE_SHA}

    # Run tests with coverage
    - npx nx affected --base=$NX_BASE --head=$NX_HEAD -t lint build e2e-ci
    - npx nx test api --coverage
    # Extract coverage percentage for GitLab badge
    - echo "Coverage is $(cat coverage/coverage-summary.json | grep -o '"pct":[^,]*' | grep -o '[0-9\.]*' | head -1)%"
  coverage: '/Coverage is (\d+(?:\.\d+)?)%/'
  artifacts:
    paths:
      - coverage/
    reports:
      coverage_report:
        coverage_format: cobertura
        path: coverage/cobertura-coverage.xml
    expire_in: 1 week

deploy:
  stage: deploy
  dependencies:
    - lint-test-build
  environment:
    name: production
  only:
    - master
  script:
    - node -e "fetch('http://*************:3000/api/box/deploy/4554a3ff50a8b15d1de8')"

bump-version:
  stage: version
  image: node:22
  dependencies:
    - lint-test-build
  script:
    - if [[ "$CI_COMMIT_MESSAGE" == "Bump version to"* ]]; then echo "Skipping version bump for version commit"; exit 0; fi
    - chmod +x scripts/version-bumper.js
    - node scripts/version-bumper.js
  only:
    - master
  when: on_success
