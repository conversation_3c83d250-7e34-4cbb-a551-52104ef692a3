import type { Express, Response as ExpressResponse } from 'express';

import { RESPONSE_TYPE } from '@api/lib/constants/response-types';
import { ERROR_RATE } from '@api/lib/constants/error-codes';
import { Response } from '@api/lib/utils';
import { Rate, ErrorCode } from '@api/models';
import { Auth as AuthService, Env } from '@api/services';

const errorResponse = ({ status, error, res }: { error: ErrorCodeI; res: ExpressResponse; status: number }) => {
  res.status(status).json(Response(RESPONSE_TYPE.ERROR, error));
};

/**
 * Rate Controller
 */
export default (router: Express) => {
  const auth = AuthService();
  const namespace = `${Env.api.namespace}/rates`;

  // @path GET /api/v1/rates
  router.get(namespace, auth.withAuthMiddleware, (_, res) => {
    Rate.findAll()
      .then((rates) => {
        res.json(Response(RESPONSE_TYPE.DATA, rates));
      })
      .catch(({ message }) => {
        errorResponse({ error: ErrorCode({ code: ERROR_RATE.FETCH, message }), res, status: 500 });
      });
  });
};
