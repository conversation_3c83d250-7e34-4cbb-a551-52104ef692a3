import type { Express, Response as ExpressResponse } from 'express';

import { RESPONSE_TYPE } from '@api/lib/constants/response-types';
import { ERROR_RATE_MONTHLY } from '@api/lib/constants/error-codes';
import { Response } from '@api/lib/utils';
import { RateMonthly, ErrorCode } from '@api/models';
import { Auth as AuthService, Env } from '@api/services';

const errorResponse = ({ status, error, res }: { error: ErrorCodeI; res: ExpressResponse; status: number }) => {
  res.status(status).json(Response(RESPONSE_TYPE.ERROR, error));
};

/**
 * RateMonthly Controller
 */
export default (router: Express) => {
  const auth = AuthService();
  const namespace = `${Env.api.namespace}/rates-monthly`;

  // @path GET /api/v1/rates-monthly?date=2024-02-29
  router.get(namespace, auth.withAuthMiddleware, (req, res) => {
    const { date } = req.query;
    
    if (!date || typeof date !== 'string') {
      return errorResponse({ 
        error: ErrorCode({ code: ERROR_RATE_MONTHLY.FETCH, message: 'Date parameter is required' }), 
        res, 
        status: 400 
      });
    }

    RateMonthly.findAll({ where: { month_end_date: date } })
      .then((rates) => {
        res.json(Response(RESPONSE_TYPE.DATA, rates));
      })
      .catch(({ message }) => {
        errorResponse({ error: ErrorCode({ code: ERROR_RATE_MONTHLY.FETCH, message }), res, status: 500 });
      });
  });
};
