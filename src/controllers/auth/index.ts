import bcrypt from 'bcrypt';
import { Response as ExpressResponse, Express } from 'express';

import { ERROR_AUTH } from '@api/lib/constants/error-codes';
import { RESPONSE_TYPE } from '@api/lib/constants/response-types';
import { Response } from '@api/lib/utils';
import { User, Rate, ErrorCode } from '@api/models';
import { Auth as AuthService, Env } from '@api/services';

const BCRYPT_SALT_ROUNDS = 12;

const errorResponse = ({ status, error, res }: { error: ErrorCodeI; res: ExpressResponse; status: number }) => {
  res.status(status).json(Response(RESPONSE_TYPE.ERROR, error));
};

/**
 * Auth Controller
 */
export default (router: Express) => {
  const namespace = `${Env.api.namespace}/auth`;
  const auth = AuthService();

  // @path GET /api/v1/auth/session
  router.get(`${namespace}/session`, auth.withAuthMiddleware, async (_, res) => {
    const { jwt, user } = res.locals;

    try {
      user.dtLogin = Math.floor(Date.now() / 1000);
      await user.save();

      const currencyRates = await Rate.findAll({});

      res.json({
        token: jwt,
        user: user.toJson(),
        currencyRates,
      });
    } catch (err: unknown) {
      const { code, message } = err as ErrorCodeI;
      errorResponse({ status: 401, error: ErrorCode({ code, message }), res });
    }
  });

  // @path POST /api/v1/auth/login
  router.post(`${namespace}/login`, async (req, res) => {
    const { email, pass } = req.body;

    try {
      const user = await User.findOne({ where: { email } });

      if (!user) {
        throw ErrorCode({ code: ERROR_AUTH.NOT_FOUND, message: `Error code: ${ERROR_AUTH.NOT_FOUND}` });
      }

      if (!bcrypt.compareSync(pass, user.pass)) {
        throw ErrorCode({ code: ERROR_AUTH.PASS, message: `Error code: ${ERROR_AUTH.PASS}` });
      }

      const jwt = auth.sign(user.id);
      const currencyRates = await Rate.findAll();

      user.dtLogin = Math.floor(Date.now() / 1000);
      user.jwt = jwt;

      await user.save();

      res.json({
        token: jwt,
        user: user.toJson(),
        currencyRates,
      });
    } catch (err: unknown) {
      const { code, message } = err as ErrorCodeI;
      errorResponse({ status: 401, error: ErrorCode({ code, message }), res });
    }
  });

  // @path POST /api/v1/register
  router.post(`${namespace}/register`, async (req, res) => {
    const { email, pass } = req.body;

    const hashedPass = bcrypt.hashSync(pass, BCRYPT_SALT_ROUNDS);
    const userAlreadyExists = await User.findOne({ where: { email } });

    if (userAlreadyExists) {
      errorResponse({ status: 422, error: ErrorCode({ code: ERROR_AUTH.EXISTS }), res });
    } else {
      const user = User({ email, pass: hashedPass });

      user
        .save() // create
        .then(async () => {
          const user = await User.findOne({ where: { email } });
          const jwt = auth.sign(user.id);
          const currencyRates = await Rate.findAll();
          user.jwt = jwt;

          user.save(); // update

          res.json({
            token: user.jwt,
            user: user.toJson(),
            currencyRates,
          });
        })
        .catch(({ code, message }) => {
          errorResponse({ status: 422, error: ErrorCode({ code, message }), res });
        });
    }
  });

  // @path POST /api/v1/password-otp
  // router.post(`${namespace}/password-otp`, async (req, res) => {
  //   let { email, otp } = req.body;

  //   // TODO: validate email

  //   let user = await User.findOne({ where: { email, otp } });

  //   if (!user) {
  //     errorResponse({ error: ErrorCode({ code: ERROR_AUTH.NOT_FOUND }), res });
  //   } else {
  //     user.otp = Otp();
  //     user.otpExpires = moment()
  //       .add(1, 'hour')
  //       .format('X');

  //     user.save().then(() => {
  //       console.log(`TODO: SEND EMAIL with OTP. [${user.otp}]`);
  //       res.status(204).json({});
  //     });
  //   }
  // });

  // @path POST /api/v1/password-reset
  // router.post(`${namespace}/password-reset`, async (req, res) => {
  //   let { email } = req.body;

  //   // TODO: validate email

  //   let user = await User.findOne({ where: { email } });

  //   if (!user) {
  //     errorResponse({ error: ErrorCode({ code: ERROR_AUTH.NOT_FOUND }), res });
  //   } else {
  //     user.otp = Otp();
  //     user.otpExpires = moment()
  //       .add(1, 'hour')
  //       .format('X');

  //     user.save().then(() => {
  //       console.log(`TODO: SEND EMAIL with OTP. [${user.otp}]`);
  //       res.status(204).json({});
  //     });
  //   }
  // });

  // @path DELETE /api/v1/auth/logout
  router.delete(`${namespace}/logout`, auth.withAuthMiddleware, async (req, res): Promise<void> => {
    try {
      const { user } = res.locals;

      user.jwt = null;
      await user.save();
      delete res.locals.user;
      delete res.locals.jwt;

      req.session.destroy(() => {
        delete req.session;
        res.status(204).send();
      });
    } catch (err: unknown) {
      const { code, message } = err as ErrorCodeI;

      errorResponse({ error: ErrorCode({ code, message }), res, status: 401 });
    }
  });
};
