import type { Express, Response as ExpressResponse } from 'express';

import type { SqlAdapterOptions } from '@api/adapters/sql';
import { Auth as AuthService, Env } from '@api/services';
import { Holding, ErrorCode } from '@api/models';
import { ERROR_HOLDING } from '@api/lib/constants/error-codes';
import { RESPONSE_TYPE } from '@api/lib/constants/response-types';
import { Response } from '@api/lib/utils';

const errorResponse = ({ status, error, res }: { error: ErrorCodeI; res: ExpressResponse; status: number }) => {
  res.status(status).json(Response(RESPONSE_TYPE.ERROR, error));
};

const isHoldingLike = (obj: unknown): boolean => {
  return typeof obj === 'object' && obj !== null && 'name' in obj && 'symbol' in obj;
};

const isValidHoldingData = (data: unknown): boolean => {
  if (Array.isArray(data)) {
    return data.every(isHoldingLike);
  }
  return isHoldingLike(data);
};

/**
 * Holding Controller
 */
export default (router: Express) => {
  const auth = AuthService();
  const namespace = `${Env.api.namespace}/holdings`;

  // @path GET /api/v1/holdings
  router.get(namespace, auth.withAuthMiddleware, (req, res) => {
    const { uid } = res.locals;
    const options: SqlAdapterOptions = {
      where: { uid },
    };

    if (req.query.sort) {
      options.sort = String(req.query.sort);
    }

    Holding.findAll(options)
      .then((holdings) => {
        res.json(Response(RESPONSE_TYPE.DATA, holdings));
      })
      .catch(({ message }) => {
        errorResponse({ res, status: 500, error: ErrorCode({ message, code: ERROR_HOLDING.FETCH }) });
      });
  });

  // @path POST /api/v1/holdings
  router.post(namespace, auth.withAuthMiddleware, async (req, res) => {
    const { uid } = res.locals;
    const raw = req.body;
    const { replace } = req.query;

    if (!isValidHoldingData(raw)) {
      errorResponse({
        res,
        status: 400,
        error: ErrorCode({
          message: Array.isArray(raw)
            ? 'Invalid holding data. Each holding must have name and symbol properties.'
            : 'Invalid holding data. Holding must have name and symbol properties.',
          code: ERROR_HOLDING.PATCH_TYPE_ALL,
        }),
      });
      return;
    }

    try {
      if (replace) {
        await Holding.deleteAll(uid);

        const savePromises: Promise<HoldingI>[] = [];

        if (Array.isArray(raw)) {
          raw.forEach((item) => {
            const holding = Holding({ ...item, uid });
            savePromises.push(holding.import());
          });
        } else {
          const holding = Holding({ ...raw, uid });
          savePromises.push(holding.import());
        }

        const holdings = await Promise.all(savePromises).then(async () => {
          return await Holding.findAll({ where: { uid } });
        });

        res.status(201).json(Response(RESPONSE_TYPE.DATA, holdings));
      } else {
        if (Array.isArray(raw)) {
          const savePromises: Promise<HoldingI>[] = [];

          raw.forEach((item) => {
            const holding = Holding({ ...item, uid });
            savePromises.push(holding.save());
          });

          const holdings = await Promise.all(savePromises).then(async () => {
            return await Holding.findAll({ where: { uid } });
          });

          res.status(201).json(Response(RESPONSE_TYPE.DATA, holdings));
        } else {
          const holding = Holding({ ...raw, uid });
          const newHolding = await holding.save();
          res.status(201).json(Response(RESPONSE_TYPE.DATA, newHolding));
        }
      }
    } catch (err: unknown) {
      const { message, code = ERROR_HOLDING.CREATE } = err as ErrorCodeI;
      errorResponse({ status: 500, res, error: ErrorCode({ code, message }) });
    }
  });

  // @path PATCH /api/v1/holdings
  router.patch(namespace, auth.withAuthMiddleware, async (req, res) => {
    const { uid } = res.locals;
    const raw = req.body;

    if (!isValidHoldingData(raw)) {
      errorResponse({
        res,
        status: 400,
        error: ErrorCode({
          message: Array.isArray(raw)
            ? 'Invalid holding data. Each holding must have name and symbol properties.'
            : 'Invalid holding data. Holding must have name and symbol properties.',
          code: ERROR_HOLDING.PATCH_TYPE_ALL,
        }),
      });
      return;
    }

    if (Array.isArray(raw)) {
      try {
        const savePromises: Promise<HoldingI>[] = [];

        raw.forEach((item) => {
          savePromises.push(Holding({ ...item, uid }).save());
        });

        const holdings = await Promise.all(savePromises).then(async () => {
          return await Holding.findAll({ where: { uid }, sort: 'position' });
        });

        res.json(Response(RESPONSE_TYPE.DATA, holdings));
      } catch (err: unknown) {
        const { message } = err as ErrorCodeI;
        errorResponse({ res, status: 500, error: ErrorCode({ code: ERROR_HOLDING.PATCH_ALL, message }) });
      }
    } else {
      try {
        const holding = Holding({ ...raw, uid });
        await holding.save();
        res.status(204).json({});
      } catch (err: unknown) {
        const { message } = err as ErrorCodeI;
        errorResponse({ res, status: 500, error: ErrorCode({ message, code: ERROR_HOLDING.PATCH }) });
      }
    }
  });

  // @path PATCH /api/v1/holdings/:id
  router.patch(`${namespace}/:id`, auth.withAuthMiddleware, async (req, res) => {
    const { uid } = res.locals;
    const { id } = req.params;
    const raw = req.body;

    if (!isValidHoldingData(raw)) {
      errorResponse({
        res,
        status: 400,
        error: ErrorCode({
          message: 'Invalid holding data. Holding must have name and symbol properties.',
          code: ERROR_HOLDING.PATCH_TYPE_ALL,
        }),
      });
      return;
    }

    const holding = await Holding.findOne({
      where: {
        id,
        uid,
      },
    });

    if (!holding) {
      return errorResponse({ res, status: 404, error: ErrorCode({ code: ERROR_HOLDING.NOT_FOUND, message: 'Holding not found' }) });
    }

    const updatedHolding = Holding({ ...holding, ...raw });

    updatedHolding
      .save()
      .then((holding) => {
        res.json(Response(RESPONSE_TYPE.DATA, holding));
      })
      .catch(({ message }) => {
        errorResponse({ res, status: 500, error: ErrorCode({ message, code: ERROR_HOLDING.UPDATE }) });
      });
  });

  // @path DELETE /api/v1/holdings/:id
  router.delete(`${namespace}/:id`, auth.withAuthMiddleware, async (req, res) => {
    const { uid } = res.locals;
    const { id } = req.params;

    try {
      const holding = await Holding.findOne({ where: { id, uid } });
      if (!holding) {
        return errorResponse({ res, status: 404, error: ErrorCode({ code: ERROR_HOLDING.NOT_FOUND, message: 'Holding not found' }) });
      }

      await Holding.delete({ id, uid });
      res.status(204).json(Response(RESPONSE_TYPE.DATA, {}));
    } catch (err: unknown) {
      const { message } = err as ErrorCodeI;
      errorResponse({ res, status: 500, error: ErrorCode({ message, code: ERROR_HOLDING.DELETE }) });
    }
  });
};
