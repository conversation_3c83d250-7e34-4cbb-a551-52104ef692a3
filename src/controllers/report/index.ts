import type { Express, Response as ExpressResponse } from 'express';

import { Auth as AuthService, Env } from '@api/services';
import { Report, ErrorCode } from '@api/models';
import { RESPONSE_TYPE } from '@api/lib/constants/response-types';
import { ERROR_REPORT } from '@api/lib/constants/error-codes';
import { Response } from '@api/lib/utils';

const errorResponse = ({ error, res, status }: { error: ErrorCodeI; res: ExpressResponse; status: number }) => {
  res.status(status).json(Response(RESPONSE_TYPE.ERROR, error));
};

const dataResponse = ({ data, res, status = 200 }: { data: ReportI | ReportI[] | null; res: ExpressResponse; status?: number }) => {
  res.status(status).json(Response(RESPONSE_TYPE.DATA, data === null ? {} : data));
};

/**
 * Report Controller
 */
export default (router: Express) => {
  const auth = AuthService();
  const namespace = `${Env.api.namespace}/reports`;

  // @path GET /api/v1/reports
  router.get(namespace, auth.withAuthMiddleware, (req, res) => {
    const { uid } = res.locals;

    Report.findAll({ where: { uid } })
      .then((reports) => {
        dataResponse({ data: reports, res });
      })
      .catch(({ message }) => {
        errorResponse({ error: ErrorCode({ code: ERROR_REPORT.FETCH, message }), res, status: 500 });
      });
  });

  // @path POST /api/v1/reports
  router.post(namespace, auth.withAuthMiddleware, async (req, res) => {
    const raw = req.body;
    const { replace } = req.query;
    const { uid } = res.locals;

    try {
      if (replace) {
        await Report.deleteAll(uid);

        if (Array.isArray(raw)) {
          await Promise.all(
            raw.map(async (item) => {
              const report = Report({ ...item, uid });
              return report.import();
            }),
          );

          const savedReports = await Report.findAll({ where: { uid } });
          dataResponse({ data: savedReports, res });
        } else {
          const report = Report({ ...raw, uid });
          await report.save();

          const newReport = await Report.findOne({ where: { id: report.id, uid } });
          if (newReport) {
            dataResponse({ data: newReport, res });
          } else {
            throw new Error('Report save operation failed silently');
          }
        }
      } else {
        if (Array.isArray(raw)) {
          await Promise.all(
            raw.map(async (item) => {
              const report = Report({ ...item, uid });
              return report.save();
            }),
          );

          const savedReports = await Report.findAll({ where: { uid } });
          dataResponse({ data: savedReports, res, status: 201 });
        } else {
          const report = Report({ ...raw, uid });
          await report.save();

          const newReport = await Report.findOne({ where: { id: report.id, uid } });
          if (newReport) {
            dataResponse({ data: newReport, res, status: 201 });
          } else {
            throw new Error('Report save operation failed silently');
          }
        }
      }
    } catch (err) {
      const { message } = err as Error;
      errorResponse({ error: ErrorCode({ message, code: ERROR_REPORT.CREATE }), res, status: 500 });
    }
  });

  // @path PATCH /api/v1/reports/:id
  router.patch(`${namespace}/:id`, auth.withAuthMiddleware, async (req, res) => {
    const { uid } = res.locals;
    const { id } = req.params;

    const rResponse = await Report.findOne({ where: { id, uid } });

    if (!rResponse) {
      return errorResponse({
        error: ErrorCode({ code: ERROR_REPORT.UPDATE, message: 'Report not found for update' }),
        res,
        status: 404,
      });
    }

    const report = Report({ ...rResponse, ...req.body });

    report
      .save()
      .then((instance) => {
        dataResponse({ data: instance, res });
      })
      .catch(({ message }) => {
        errorResponse({ error: ErrorCode({ message, code: ERROR_REPORT.UPDATE }), res, status: 500 });
      });
  });

  // @path DELETE /api/v1/reports/:id
  router.delete(`${namespace}/:id`, auth.withAuthMiddleware, async (req, res) => {
    const { uid } = res.locals;
    const { id } = req.params;

    try {
      const result = (await Report.delete({ id, uid })) as { affectedRows: number };

      if (!result || result.affectedRows === 0) {
        errorResponse({
          error: ErrorCode({ code: ERROR_REPORT.DELETE, message: 'Report not found for deletion' }),
          res,
          status: 404,
        });
      } else {
        dataResponse({ data: null, res, status: 204 });
      }
    } catch (err) {
      const { message } = err as Error;
      errorResponse({ error: ErrorCode({ message, code: ERROR_REPORT.DELETE }), res, status: 500 });
    }
  });
};
