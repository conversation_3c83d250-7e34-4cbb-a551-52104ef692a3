import type { Express } from 'express';
import cron from 'node-cron';

import { TIMEZONE } from '@api/lib/constants';
import { currencies } from '@api/lib/constants/currencies';
import { Rate, RateMonthly } from '@api/models';
import { fetchCurrenciesTask } from '@api/services/tasks';
import { Env } from '@api/services';
import { shouldSaveMonthlyRates, logMarketCheckResults } from '@api/lib/utils/market-utils';
const everyDay = '0 17 * * *'; // 17:00 at New York timezone

export default (router: Express) => {
  cron.schedule(
    everyDay,
    () => {
      console.log(`Running a job at 03:00 at ${TIMEZONE} timezone`);

      // Check if we should save monthly rates
      const marketCheck = shouldSaveMonthlyRates();
      logMarketCheckResults(marketCheck);

      fetchCurrenciesTask()
        .then((currencyData) => {
          const savePromises = [];
          const monthlyRatePromises = [];

          for (let i = 0, len = currencyData.length; i < len; i++) {
            const item = currencyData[i];
            const currency = currencies.find((c) => c.code === item.code);

            if (currency) {
              const roundedRate = Math.round(item.rate * 10000000000) / 10000000000; // round up to 10 decimal places

              // Always save to currency_rates table
              const rate = Rate({
                id: currency.id,
                code: currency.code,
                rate: roundedRate,
              });

              savePromises.push(rate.save());

              // Save to currency_rates_monthly table if conditions are met
              if (marketCheck.shouldSave) {
                const monthlyRate = RateMonthly({
                  currency_id: currency.id,
                  month_end_date: marketCheck.monthEndDate,
                  rate: roundedRate,
                });

                monthlyRatePromises.push(monthlyRate.save());
              }
            }
          }

          // Save all regular rates
          const allPromises = [Promise.all(savePromises)];

          // Add monthly rates promises if we should save them
          if (marketCheck.shouldSave && monthlyRatePromises.length > 0) {
            allPromises.push(Promise.all(monthlyRatePromises));
            console.log(`Saving ${monthlyRatePromises.length} monthly rates for ${marketCheck.monthEndDate}`);
          }

          return Promise.all(allPromises);
        })
        .catch((err: unknown) => {
          const { message } = err as Error;
          console.error({ error: message });
          // res.status(500).json({ error: e.message });
        });
    },
    {
      scheduled: true,
      timezone: TIMEZONE,
    },
  );

  const namespace = `${Env.api.namespace}/tasks`;

  // @path GET /api/v1/tasks/currencies
  router.get(`${namespace}/currencies`, (req, res) => {
    fetchCurrenciesTask()
      .then((currencyData) => {
        res.json(currencyData);
      })
      .catch((err: unknown) => {
        const { message } = err as Error;
        res.status(500).json({ error: message });
      });
  });

  // @path POST /api/v1/tasks/currencies/update
  router.post(`${namespace}/currencies/update`, (req, res) => {
    // Check if we should save monthly rates
    const marketCheck = shouldSaveMonthlyRates();
    logMarketCheckResults(marketCheck);

    fetchCurrenciesTask()
      .then((currencyData) => {
        const savePromises = [];
        const monthlyRatePromises = [];

        for (let i = 0, len = currencyData.length; i < len; i++) {
          const item = currencyData[i];
          const currency = currencies.find((c) => c.code === item.code);

          if (currency) {
            const roundedRate = Math.round(item.rate * 10000000000) / 10000000000; // round up to 10 decimal places

            // Always save to currency_rates table
            const rate = Rate({
              id: currency.id,
              code: currency.code,
              rate: roundedRate,
            });

            savePromises.push(rate.save());

            // Save to currency_rates_monthly table if conditions are met
            if (marketCheck.shouldSave) {
              const monthlyRate = RateMonthly({
                currency_id: currency.id,
                month_end_date: marketCheck.monthEndDate,
                rate: roundedRate,
              });

              monthlyRatePromises.push(monthlyRate.save());
            }
          }
        }

        // Save all regular rates
        const allPromises = [Promise.all(savePromises)];

        // Add monthly rates promises if we should save them
        if (marketCheck.shouldSave && monthlyRatePromises.length > 0) {
          allPromises.push(Promise.all(monthlyRatePromises));
          console.log(`Saving ${monthlyRatePromises.length} monthly rates for ${marketCheck.monthEndDate}`);
        }

        return Promise.all(allPromises);
      })
      .then(() => {
        res.status(204).json({});
      })
      .catch((err: unknown) => {
        const { message } = err as Error;
        res.status(500).json({ error: message });
      });
  });

  // @path GET /api/v1/tasks/market-check
  router.get(`${namespace}/market-check`, (req, res) => {
    const marketCheck = shouldSaveMonthlyRates();
    res.json(marketCheck);
  });
};
