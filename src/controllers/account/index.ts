import type { Express, Response as ExpressResponse } from 'express';

import type { SqlAdapterOptions } from '@api/adapters/sql';
import { Auth as AuthService, Env } from '@api/services';
import { Account, ErrorCode } from '@api/models';
import { ERROR_ACCOUNT } from '@api/lib/constants/error-codes';
import { RESPONSE_TYPE } from '@api/lib/constants/response-types';
import { Response } from '@api/lib/utils';

const errorResponse = ({ status, error, res }: { error: ErrorCodeI; res: ExpressResponse; status: number }) => {
  res.status(status).json(Response(RESPONSE_TYPE.ERROR, error));
};

const isAccountLike = (obj: unknown): boolean => {
  return typeof obj === 'object' && obj !== null && 'taxTreatment' in obj;
};

const isValidAccountData = (data: unknown): boolean => {
  if (Array.isArray(data)) {
    return data.every(isAccountLike);
  }
  return isAccountLike(data);
};

/**
 * Account Controller
 */
export default (router: Express) => {
  const auth = AuthService();
  const namespace = `${Env.api.namespace}/accounts`;

  // @path GET /api/v1/accounts
  router.get(namespace, auth.withAuthMiddleware, (req, res) => {
    const { uid } = res.locals;
    const options: SqlAdapterOptions = {
      where: { uid },
    };

    if (req.query.sort) {
      options.sort = String(req.query.sort);
    }

    Account.findAll(options)
      .then((accounts) => {
        res.json(Response(RESPONSE_TYPE.DATA, accounts));
      })
      .catch(({ message }) => {
        errorResponse({ res, status: 500, error: ErrorCode({ message, code: ERROR_ACCOUNT.FETCH }) });
      });
  });

  // @path POST /api/v1/accounts
  router.post(namespace, auth.withAuthMiddleware, async (req, res) => {
    const { uid } = res.locals;
    const raw = req.body;
    const { replace } = req.query;

    if (!isValidAccountData(raw)) {
      errorResponse({
        res,
        status: 400,
        error: ErrorCode({
          message: Array.isArray(raw)
            ? 'Invalid account data. Each account must have name property.'
            : 'Invalid account data. Account must have name property.',
          code: ERROR_ACCOUNT.PATCH_TYPE_ALL,
        }),
      });
      return;
    }

    try {
      if (replace) {
        await Account.deleteAll(uid);

        const savePromises: Promise<AccountI>[] = [];

        if (Array.isArray(raw)) {
          raw.forEach((item) => {
            const account = Account({ ...item, uid });
            savePromises.push(account.import());
          });
        } else {
          const account = Account({ ...raw, uid });
          savePromises.push(account.import());
        }

        const accounts = await Promise.all(savePromises).then(async () => {
          return await Account.findAll({ where: { uid } });
        });

        res.status(201).json(Response(RESPONSE_TYPE.DATA, accounts));
      } else {
        if (Array.isArray(raw)) {
          const savePromises: Promise<AccountI>[] = [];

          raw.forEach((item) => {
            const account = Account({ ...item, uid });
            savePromises.push(account.save());
          });

          const accounts = await Promise.all(savePromises).then(async () => {
            return await Account.findAll({ where: { uid } });
          });

          res.status(201).json(Response(RESPONSE_TYPE.DATA, accounts));
        } else {
          const account = Account({ ...raw, uid });
          const newAccount = await account.save();
          res.status(201).json(Response(RESPONSE_TYPE.DATA, newAccount));
        }
      }
    } catch (err: unknown) {
      const { message, code = ERROR_ACCOUNT.CREATE } = err as ErrorCodeI;
      errorResponse({ status: 500, res, error: ErrorCode({ code, message }) });
    }
  });

  // @path PATCH /api/v1/accounts
  router.patch(namespace, auth.withAuthMiddleware, async (req, res) => {
    const { uid } = res.locals;
    const raw = req.body;

    if (!isValidAccountData(raw)) {
      errorResponse({
        res,
        status: 400,
        error: ErrorCode({
          message: Array.isArray(raw)
            ? 'Invalid account data. Each account must have name property.'
            : 'Invalid account data. Account must have name property.',
          code: ERROR_ACCOUNT.PATCH_TYPE_ALL,
        }),
      });
      return;
    }

    if (Array.isArray(raw)) {
      try {
        const savePromises: Promise<AccountI>[] = [];

        raw.forEach((item) => {
          savePromises.push(Account({ ...item, uid }).save());
        });

        const accounts = await Promise.all(savePromises).then(async () => {
          return await Account.findAll({ where: { uid } });
        });

        res.json(Response(RESPONSE_TYPE.DATA, accounts));
      } catch (err: unknown) {
        const { message } = err as ErrorCodeI;
        errorResponse({ res, status: 500, error: ErrorCode({ code: ERROR_ACCOUNT.PATCH_ALL, message }) });
      }
    } else {
      try {
        const account = Account({ ...raw, uid });
        await account.save();
        res.status(204).json({});
      } catch (err: unknown) {
        const { message } = err as ErrorCodeI;
        errorResponse({ res, status: 500, error: ErrorCode({ message, code: ERROR_ACCOUNT.PATCH }) });
      }
    }
  });

  // @path PATCH /api/v1/accounts/:id
  router.patch(`${namespace}/:id`, auth.withAuthMiddleware, async (req, res) => {
    const { uid } = res.locals;
    const { id } = req.params;
    const raw = req.body;

    if (!isValidAccountData(raw)) {
      errorResponse({
        res,
        status: 400,
        error: ErrorCode({
          message: 'Invalid account data. Account must have name property.',
          code: ERROR_ACCOUNT.PATCH_TYPE_ALL,
        }),
      });
      return;
    }

    const account = await Account.findOne({
      where: {
        id: Number(id),
        uid,
      },
    });

    if (!account) {
      return errorResponse({ res, status: 404, error: ErrorCode({ code: ERROR_ACCOUNT.NOT_FOUND, message: 'Account not found' }) });
    }

    const updatedAccount = Account({ ...account, ...raw });

    updatedAccount
      .save()
      .then((account) => {
        res.json(Response(RESPONSE_TYPE.DATA, account));
      })
      .catch(({ message }) => {
        errorResponse({ res, status: 500, error: ErrorCode({ message, code: ERROR_ACCOUNT.UPDATE }) });
      });
  });

  // @path DELETE /api/v1/accounts/:id
  router.delete(`${namespace}/:id`, auth.withAuthMiddleware, async (req, res) => {
    const { uid } = res.locals;
    const { id } = req.params;

    try {
      const account = await Account.findOne({ where: { id: Number(id), uid } });
      if (!account) {
        return errorResponse({ res, status: 404, error: ErrorCode({ code: ERROR_ACCOUNT.NOT_FOUND, message: 'Account not found' }) });
      }

      await Account.delete({ id: Number(id), uid });
      res.status(204).json(Response(RESPONSE_TYPE.DATA, {}));
    } catch (err: unknown) {
      const { message } = err as ErrorCodeI;
      errorResponse({ res, status: 500, error: ErrorCode({ message, code: ERROR_ACCOUNT.DELETE }) });
    }
  });
};
