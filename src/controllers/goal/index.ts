import type { Express, Request as ExpressRequest, Response as ExpressResponse } from 'express';

import type { SqlAdapterOptions } from '@api/adapters/sql';
import { RESPONSE_TYPE } from '@api/lib/constants/response-types';
import { ERROR_GOAL } from '@api/lib/constants/error-codes';
import { Response } from '@api/lib/utils';
import { Goal, ErrorCode } from '@api/models';
import { Auth as AuthService, Env } from '@api/services';

const errorResponse = ({ status, error, res }: { error: ErrorCodeI; res: ExpressResponse; status: number }) => {
  res.status(status).json(Response(RESPONSE_TYPE.ERROR, error));
};

const isGoalLike = (obj: unknown): boolean => {
  return typeof obj === 'object' && obj !== null && 'networth' in obj && 'dividends' in obj;
};

const isValidGoalData = (data: unknown): boolean => {
  if (Array.isArray(data)) {
    return data.every(isGoalLike);
  }
  return isGoalLike(data);
};

/**
 * Goal Controller
 */
export default (router: Express) => {
  const auth = AuthService();
  const namespace = `${Env.api.namespace}/goals`;

  // @path GET /api/v1/goals
  router.get(namespace, auth.withAuthMiddleware, (req: ExpressRequest, res: ExpressResponse) => {
    const { uid } = res.locals;
    const options: SqlAdapterOptions = {
      where: { uid },
    };

    Goal.findAll(options)
      .then((goals) => {
        res.status(200).json(Response(RESPONSE_TYPE.DATA, goals));
      })
      .catch(({ message }) => {
        errorResponse({ status: 500, res, error: ErrorCode({ message, code: ERROR_GOAL.FETCH }) });
      });
  });

  // @path POST /api/v1/goals
  router.post(namespace, auth.withAuthMiddleware, async (req, res) => {
    const { uid } = res.locals;
    const raw = req.body;
    const { replace } = req.query;

    if (!isValidGoalData(raw)) {
      errorResponse({
        res,
        status: 400,
        error: ErrorCode({
          message: Array.isArray(raw)
            ? 'Invalid goal data. Each goal must have networth and dividends properties.'
            : 'Invalid goal data. Goal must have networth and dividends properties.',
          code: ERROR_GOAL.PATCH_TYPE_ALL,
        }),
      });
      return;
    }

    try {
      if (replace) {
        await Goal.deleteAll(uid);

        const savePromises: Promise<GoalI>[] = [];

        if (Array.isArray(raw)) {
          raw.forEach((item) => {
            const goal = Goal({ ...item, uid });
            savePromises.push(goal.import());
          });
        } else {
          const goal = Goal({ ...raw, uid });
          savePromises.push(goal.import());
        }

        const goals = await Promise.all(savePromises).then(async () => {
          return await Goal.findAll({ where: { uid } });
        });

        res.status(201).json(Response(RESPONSE_TYPE.DATA, goals));
      } else {
        if (Array.isArray(raw)) {
          const savePromises: Promise<GoalI>[] = [];

          raw.forEach((item) => {
            const goal = Goal({ ...item, uid });
            savePromises.push(goal.save());
          });

          const goals = await Promise.all(savePromises).then(async () => {
            return await Goal.findAll({ where: { uid } });
          });

          res.status(201).json(Response(RESPONSE_TYPE.DATA, goals));
        } else {
          const goal = Goal({ ...raw, uid });
          const newGoal = await goal.save();
          res.status(201).json(Response(RESPONSE_TYPE.DATA, newGoal));
        }
      }
    } catch (err: unknown) {
      const { message, code = ERROR_GOAL.CREATE } = err as ErrorCodeI;
      errorResponse({ status: 500, res, error: ErrorCode({ code, message }) });
    }
  });

  // @path PATCH /api/v1/goals/:id
  router.patch(`${namespace}/:id`, auth.withAuthMiddleware, async (req, res) => {
    const { uid } = res.locals;
    const { id } = req.params;
    const raw = req.body;

    if (!isValidGoalData(raw)) {
      errorResponse({
        res,
        status: 400,
        error: ErrorCode({
          message: 'Invalid goal data. Goal must have networth and dividends properties.',
          code: ERROR_GOAL.PATCH_TYPE_ALL,
        }),
      });
      return;
    }

    const goal = await Goal.findOne({
      where: {
        id,
        uid,
      },
    });

    if (!goal) {
      return errorResponse({ res, status: 404, error: ErrorCode({ code: ERROR_GOAL.NOT_FOUND, message: 'Goal not found' }) });
    }

    const updatedGoal = Goal({ ...goal, ...raw });

    updatedGoal
      .save()
      .then((goal) => {
        res.json(Response(RESPONSE_TYPE.DATA, goal));
      })
      .catch(({ message }) => {
        errorResponse({ res, status: 500, error: ErrorCode({ message, code: ERROR_GOAL.UPDATE }) });
      });
  });

  // @path DELETE /api/v1/goals/:id
  router.delete(`${namespace}/:id`, auth.withAuthMiddleware, async (req, res) => {
    const { uid } = res.locals;
    const { id } = req.params;

    try {
      const goal = await Goal.findOne({ where: { id, uid } });
      if (!goal) {
        return errorResponse({ res, status: 404, error: ErrorCode({ code: ERROR_GOAL.NOT_FOUND, message: 'Goal not found' }) });
      }

      await Goal.delete({ id, uid });
      res.status(204).json(Response(RESPONSE_TYPE.DATA, {}));
    } catch (err: unknown) {
      const { message } = err as ErrorCodeI;
      errorResponse({ res, status: 500, error: ErrorCode({ message, code: ERROR_GOAL.DELETE }) });
    }
  });
};
