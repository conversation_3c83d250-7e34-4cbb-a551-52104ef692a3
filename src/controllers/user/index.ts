import type { Express, Response as ExpressResponse } from 'express';

import { RESPONSE_TYPE } from '@api/lib/constants/response-types';
import { ERROR_USER } from '@api/lib/constants/error-codes';
import { Response } from '@api/lib/utils';
import { User, ErrorCode } from '@api/models';
import { Auth as AuthService, Env } from '@api/services';

const errorResponse = ({ error, res, status }: { error: ErrorCodeI; res: ExpressResponse; status: number }) => {
  res.status(status).json(Response(RESPONSE_TYPE.ERROR, error));
};

const dataResponse = ({ data, res, status }: { data: UserJson; res: ExpressResponse; status?: number }) => {
  res.status(status).json(Response(RESPONSE_TYPE.DATA, data));
};

/**
 * User Controller
 */
export default (router: Express) => {
  const auth = AuthService();
  const namespace = `${Env.api.namespace}/users`;

  // @path PATCH /api/v1/users/:id
  router.patch(`${namespace}/:id`, auth.withAuthMiddleware, async (req, res) => {
    const { uid } = res.locals;
    const { id } = req.params;

    if (id !== uid) {
      return errorResponse({ error: ErrorCode({ code: ERROR_USER.WRONG_UID }), res, status: 401 });
    }

    try {
      const uResponse = await User.findOne({
        where: {
          id,
        },
      });

      const user = User({ ...uResponse, ...req.body });
      await user.save();

      const updatedUser = await User.findOne({ where: { id } });

      if (updatedUser) {
        dataResponse({ data: updatedUser.toJson(), res, status: 200 });
      } else {
        errorResponse({ error: ErrorCode({ message: 'User update failed', code: ERROR_USER.UPDATE }), res, status: 500 });
      }
    } catch (err) {
      errorResponse({ error: ErrorCode({ message: err.message, code: ERROR_USER.FETCH }), res, status: 500 });
    }
  });
};
