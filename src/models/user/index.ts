import SqlAdapter, { SqlAdapterOptions } from '@api/adapters/sql';
import currencies, { CURRENCY_USD } from '@api/lib/constants/currencies';
import { Uuid, toJson } from '@api/lib/utils';

const table = 'users';
const hidden = ['jwt', 'otp', 'otpExpires', 'pass'];
const adapter = SqlAdapter({ table });

export type UserProps = UserJson & {
  id?: string;
  jwt: string;
  otp: string;
  otpExpires: number;
  pass: string;
};

const User = (attrs: Partial<UserProps>): UserI => {
  const instance: Partial<UserI> = {
    currency: attrs.currency && currencies.find((id) => id === attrs.currency) ? attrs.currency : CURRENCY_USD,
    dtCreated: attrs.dtCreated,
    dtLogin: attrs.dtLogin,
    email: attrs.email,
    id: attrs.id,
    jwt: attrs.jwt,
    licence: attrs.licence,
    otp: attrs.otp,
    otpExpires: attrs.otpExpires,
    pass: attrs.pass,
  };

  Object.defineProperty(instance, 'isNew', {
    enumerable: false,
    value: !instance.id,
  });

  Object.defineProperty(instance, 'toJson', {
    enumerable: false,
    value: () => toJson(instance, { hidden }),
  });

  /**
   * Smart save: create/update in one
   */
  Object.defineProperty(instance, 'save', {
    enumerable: false,
    value: () => {
      if (instance.isNew) {
        instance.id = Uuid();
        instance.dtCreated = Math.floor(Date.now() / 1000);

        return adapter.create(instance);
      } else {
        return adapter.update(instance, { where: { id: instance.id } });
      }
    },
  });

  return Object.seal(instance) as UserI;
};

User.findOne = (options: SqlAdapterOptions) =>
  adapter.findOne(options).then((record) => (record ? User(record as Partial<UserJson>) : null));

User.delete = (id: string) => adapter.delete({ where: { id } });

export default User;
