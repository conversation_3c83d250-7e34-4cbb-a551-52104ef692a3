import { Uuid, toJson } from '@api/lib/utils';
import SqlAdapter, { SqlAdapterOptions } from '@api/adapters/sql';
import currencies, { CURRENCY_USD } from '@api/lib/constants/currencies';

const table = 'holdings';
const adapter = SqlAdapter({ table });

type HoldingProps = HoldingJson & {
  id?: string;
};

const Holding = (attrs: Partial<HoldingProps>): HoldingI => {
  const instance: Partial<HoldingI> = {
    count: Number(attrs.count ?? 0),
    currency: attrs.currency && currencies.find((id) => id === attrs.currency) ? attrs.currency : CURRENCY_USD,
    dividend: Number(attrs.dividend ?? 0),
    dividendCurrency:
      attrs.dividendCurrency && currencies.find((id) => id === attrs.dividendCurrency) ? attrs.dividendCurrency : CURRENCY_USD,
    dividendFrequency: attrs.dividendFrequency,
    dividendTaxRate: Number(attrs.dividendTaxRate ?? 0),
    id: attrs.id,
    isDeleted: attrs.isDeleted,
    name: attrs.name,
    position: attrs.position ?? 0,
    price: Number(attrs.price ?? 0),
    symbol: attrs.symbol,
    uid: attrs.uid,
  };

  Object.defineProperty(instance, 'isNew', {
    enumerable: false,
    value: !instance.id,
  });

  Object.defineProperty(instance, 'toJson', {
    enumerable: false,
    value: () => toJson(instance),
  });

  /**
   * Smart save: create/update in one
   */
  Object.defineProperty(instance, 'save', {
    enumerable: false,
    value: () => {
      if (instance.isNew) {
        instance.id = Uuid();

        return adapter.create(instance).then(() => instance);
      } else {
        return adapter.update(instance, { where: { id: instance.id, uid: instance.uid } }).then(() => instance);
      }
    },
  });

  /**
   * import/create
   */
  Object.defineProperty(instance, 'import', {
    enumerable: false,
    value: () => adapter.create(instance).then(() => instance),
  });

  return Object.seal(instance as HoldingI);
};

Holding.findOne = (options: SqlAdapterOptions) =>
  adapter.findOne(options).then((record) => (record ? Holding(record as HoldingJson) : null));

Holding.findAll = (options: SqlAdapterOptions) => {
  return adapter.findAll(options).then((records) => {
    return records?.map((record: HoldingJson) => Holding(record));
  });
};

Holding.delete = ({ id, uid }) => adapter.delete({ where: { id, uid } });
Holding.deleteAll = (uid: string) => adapter.delete({ where: { uid } });

/**
 * Static save all
 * @param {Array<Holding>} holdings
 * @return {Promise}
 */
// Holding.saveAll = (holdings) => {
//   if (!holdings || (!Array.isArray(holdings) && holdings.length === 0)) {
//     throw new Error('SaveAll method expected an array as parameter.');
//   }

//   let [h] = holdings;

//   // whole array has to be either new or existing since additions are happening one at a time for new
//   // or through import for not new
//   if (h.isNew) {
//     return adapter.create(holdings).then(() => holdings);
//   } else {
//     return adapter.update(holdings).then(() => holdings);
//   }
// };

export default Holding;
