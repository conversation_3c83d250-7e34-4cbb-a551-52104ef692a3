export const CURRENCY_AUD = 8;
export const CURRENCY_CAD = 2;
export const CURRENCY_CHF = 9;
export const CURRENCY_CNY = 7;
export const CURRENCY_EUR = 4;
export const CURRENCY_GBP = 5;
export const CURRENCY_INR = 10;
export const CURRENCY_JPY = 6;
export const CURRENCY_RUB = 3;
export const CURRENCY_USD = 1;

export const currencies: Currency[] = [
  { id: CURRENCY_AUD, code: 'AUD', symbol: 'a$' },
  { id: CURRENCY_CAD, code: 'CAD', symbol: 'c$' },
  { id: CURRENCY_CHF, code: 'CHF', symbol: '₣' },
  { id: CURRENCY_CNY, code: 'CNY', symbol: '¥' },
  { id: CURRENCY_EUR, code: 'EUR', symbol: '€' },
  { id: CURRENCY_GBP, code: 'GBP', symbol: '£' },
  { id: CURRENCY_INR, code: 'INR', symbol: '₹' },
  { id: CURRENCY_JPY, code: 'JPY', symbol: 'JP¥' },
  { id: CURRENCY_RUB, code: 'RUB', symbol: '₽' },
  { id: CURRENCY_USD, code: 'USD', symbol: '$' },
];

export default [
  CURRENCY_AUD,
  CURRENCY_CAD,
  CURRENCY_CHF,
  CURRENCY_CNY,
  CURRENCY_EUR,
  CURRENCY_GBP,
  CURRENCY_INR,
  CURRENCY_JPY,
  CURRENCY_RUB,
  CURRENCY_USD,
];

export enum CURRENCY {
  AUD = 8,
  CAD = 2,
  CHF = 9,
  CNY = 7,
  EUR = 4,
  GBP = 5,
  INR = 10,
  JPY = 6,
  RUB = 3,
  USD = 1,
}
