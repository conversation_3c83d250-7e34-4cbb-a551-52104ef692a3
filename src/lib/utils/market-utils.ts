import { TIMEZONE } from '../constants';

/**
 * Market and date utility functions for currency rate tasks
 */

/**
 * Check if the current date is the last day of the month
 * @param date - Date to check (defaults to current date)
 * @returns boolean - true if it's the last day of the month
 */
export const isLastDayOfMonth = (date: Date): boolean => {
  const nextDay = new Date(date);
  nextDay.setDate(date.getDate() + 1);

  // If adding one day changes the month, then current date is the last day
  return nextDay.getMonth() !== date.getMonth();
};

/**
 * Check if USA exchanges (NASDAQ) are closed
 * NASDAQ closes at 4:00 PM ET (Eastern Time)
 * @param date - Date to check (defaults to current date)
 * @returns boolean - true if exchanges are closed
 */
export const areUSAExchangesClosed = (date: Date): boolean => {
  // Convert current time to Eastern Time
  const easternTime = new Date(date.toLocaleString('en-US', { timeZone: TIMEZONE }));

  const day = easternTime.getDay(); // 0 = Sunday, 6 = Saturday
  const hours = easternTime.getHours();
  const minutes = easternTime.getMinutes();

  // Check if it's weekend (Saturday or Sunday)
  if (day === 0 || day === 6) {
    return true;
  }

  // Check if it's before 9:30 AM ET (market opens)
  if (hours < 9 || (hours === 9 && minutes < 30)) {
    return true;
  }

  // Check if it's after 4:00 PM ET (market closes)
  if (hours >= 16) {
    return true;
  }

  // Market is open
  return false;
};

/**
 * Get the month end date string in YYYY-MM-DD format
 * @param date - Date to get month end for (defaults to current date)
 * @returns string - Month end date in YYYY-MM-DD format
 */
export const getMonthEndDateString = (date: Date): string => {
  const year = date.getFullYear();
  const month = date.getMonth();

  // Get the last day of the current month
  const lastDay = new Date(year, month + 1, 0);

  const monthStr = String(lastDay.getMonth() + 1).padStart(2, '0');
  const dayStr = String(lastDay.getDate()).padStart(2, '0');

  return `${year}-${monthStr}-${dayStr}`;
};

/**
 * Check if all conditions are met for saving monthly rates
 * @param date - Date to check (defaults to current date)
 * @returns object with check results and overall status
 */
export const shouldSaveMonthlyRates = (date: Date = new Date()) => {
  const isLastDay = isLastDayOfMonth(date);
  const exchangesClosed = areUSAExchangesClosed(date);

  return {
    isLastDayOfMonth: isLastDay,
    areExchangesClosed: exchangesClosed,
    shouldSave: isLastDay && exchangesClosed,
    monthEndDate: getMonthEndDateString(date),
    checkTime: date.toISOString(),
  };
};

/**
 * Log the market check results
 * @param checkResult - Result from shouldSaveMonthlyRates
 */
export const logMarketCheckResults = (checkResult: ReturnType<typeof shouldSaveMonthlyRates>) => {
  console.log('=== Monthly Currency Rates Check ===');
  console.log(`Check Time: ${checkResult.checkTime}`);
  console.log(`Is Last Day of Month: ${checkResult.isLastDayOfMonth}`);
  console.log(`Are USA Exchanges Closed: ${checkResult.areExchangesClosed}`);
  console.log(`Month End Date: ${checkResult.monthEndDate}`);
  console.log(`Should Save Monthly Rates: ${checkResult.shouldSave}`);
  console.log('=====================================');
};
