export { default as Otp } from '@api/lib/utils/otp';
export { default as Response } from '@api/lib/utils/response';
export { default as Uuid } from '@api/lib/utils/uuid';

// @ts-expect-error - TS doesn't play nicely with math e numbers
export const round = (value: number, precision = 2) => Number(`${Math.round(`${value}e${precision}`)}e-${precision}`);

/**
 * @param {Array} array
 * @param {String} valuePath
 */
export const uniq = (array: string[] | number[] | DynamicObject[], valuePath: string | null = null) => {
  const ret: string[] | number[] = [];

  for (let i = 0, len = array.length; i < len; i++) {
    const item: string | number = valuePath ? array[i][valuePath] : array[i];

    // @ts-expect-error - don't know how to type this shit
    if (!ret.includes(item)) {
      // @ts-expect-error - don't know how to type this shit
      ret.push(item);
    }
  }

  return ret;
};

export const sortByKey = (key: string) => (a, b) => {
  let ret = 0;

  if (a[key] < b[key]) {
    ret = -1;
  } else if (a[key] > b[key]) {
    ret = 1;
  }

  return ret;
};

export const base64Encode = (data: object | object[]) => Buffer.from(JSON.stringify(data)).toString('base64'); // btoa(JSON.stringify(data));

export const base64Decode = (b64Encoded: string) => JSON.parse(Buffer.from(b64Encoded, 'base64').toString()); // JSON.parse(atob(str));

export const isEmpty = (val: unknown) => val == null || val == '' || (Array.isArray(val) && val.length === 0);

type toJsonOptions = {
  hidden?: string[];
};

export const toJson = (data: DynamicObject, options?: toJsonOptions) => {
  if (!data || typeof data !== 'object') {
    throw new Error('Object serializer is missing data or it is not an object.');
  }

  let ret = data;

  if (options?.hidden?.length && options.hidden.length > 0) {
    ret = Object.keys(data).reduce((ret: DynamicObject, key: string) => {
      if (!options.hidden.includes(key)) {
        ret[key] = data[key];
      }

      return ret;
    }, {});
  }

  return JSON.parse(JSON.stringify(ret));
};
