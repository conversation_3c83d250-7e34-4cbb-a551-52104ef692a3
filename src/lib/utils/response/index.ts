import { RESPONSE_TYPE } from '@api/lib/constants/response-types';

const responseData = (data: object | object[]) => ({
  data
});

const responseError = (error: ErrorCodeI) => ({
  error
});

export default (type: RESPONSE_TYPE, data: ErrorCodeI | object | object[]) => {
  if (type === RESPONSE_TYPE.ERROR) {
    return responseError(data as ErrorCodeI);
  } else {
    return responseData(data);
  }
};
