import 'tsconfig-paths/register';

export const escapeKey = (key: string | number) => `\`${key}\``;

// export const escapeVal = (val: string | number) => {
//   let ret: string | number;

//   if (val == null || String(val).trim() === '') {
//     ret = 'NULL';
//   } else if (typeof val === 'number') {
//     ret = val;
//   } else {
//     ret = `'${val}'`;
//   }

//   return ret;
// };

export const getKeys = (data: object) => {
  return Object.keys(data)
    .map((key) => escapeKey(key))
    .join(', ');
};

// export const getValues = (data: object) => {
//   return Object.values(data)
//     .map((val) => escapeVal(val))
//     .join(', ');
// };

export const sqlSelect = (table: string) => `SELECT * FROM ${table}`;

export const sqlWhere = (where: object) =>
  `WHERE ${Object.keys(where)
    .map((key) => `${escapeKey(key)}=?`)
    .join(' AND ')}`;

export const sqlParams = (where: object) => Object.values(where);

export const sqlSort = (sort: string) => {
  const sortDir = String(sort).charAt(0) === '-' ? 'DESC' : 'ASC';
  const sortBy = String(sort).replace('-', '');

  return `ORDER BY ${escapeKey(sortBy)} ${sortDir}`;
};
