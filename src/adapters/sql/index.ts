import type { RowDataPacket } from 'mysql2';

import db from '@api/connection';

export type SqlAdapterI = {
  findOne: (options?: SqlAdapterOptions) => Promise<RowDataPacket | RowDataPacket[] | null>;
  findAll: (options?: SqlAdapterOptions) => Promise<RowDataPacket | RowDataPacket[]>;
  create: (data: DynamicObject) => Promise<RowDataPacket | RowDataPacket[]>;
  update: (data: DynamicObject, options: SqlAdapterOptions) => Promise<RowDataPacket | RowDataPacket[]>;
  delete: (options: SqlAdapterOptions) => Promise<RowDataPacket | RowDataPacket[]>;
};

export type SqlAdapterOptions = {
  sort?: string;
  where?:
    | Record<string, never>
    | {
        [key: string]: unknown;
      };
};

const escapeKey = (k: string) => `\`${k}\``;

const assembleWhere = (where: SqlAdapterOptions['where']): string =>
  `WHERE ${Object.keys(where)
    .map((key) => `${escapeKey(key)}=?`)
    .join(' AND ')}`;

const request = (query: string, params: unknown[]): Promise<RowDataPacket[]> => {
  return new Promise(function (resolve, reject) {
    db.query(query, params as never[], (error, result: RowDataPacket[]) => {
      if (error) {
        reject(error);
      }

      resolve(result);
    });

    // db.getConnection(function (error, connection) {
    //   if (error) {
    //     reject(error);
    //   }

    //   connection.query(query, params as never[], (error, result: RowDataPacket[]) => {
    //     connection.release(); // put connection back in pool

    //     if (error) {
    //       reject(error);
    //     }

    //     resolve(result);
    //   });
    // });
  });
};

type Props = {
  table?: string;
};

/**
 * SQL Adapter
 */
export default ({ table }: Props = {}): SqlAdapterI => {
  if (!(table && typeof table === 'string')) {
    throw new Error('SQL Adapter requires TABLE name specified.');
  }

  return {
    findOne: async ({ where }: SqlAdapterOptions = {}) => {
      if (!where || typeof where !== 'object') {
        throw new Error('SQL "Apdater.findOne()" method requires "options" and "where" attribute.');
      }

      const params = Object.values(where);
      const query = `SELECT * FROM ${table} ${assembleWhere(where)} LIMIT 1;`;

      const response = await request(query, params);

      return (Array.isArray(response) ? response[0] : response) || null;
    },

    findAll: ({ sort, where }: SqlAdapterOptions = {}) => {
      let whereQuery = '';
      let sortQuery = '';
      let params: unknown[] = [];

      // apply where
      if (where && typeof where === 'object') {
        whereQuery = assembleWhere(where);
        params = Object.values(where);
      }

      // apply sort
      if (sort) {
        const sortDir = String(sort).charAt(0) === '-' ? 'DESC' : 'ASC';
        const sortBy = String(sort).replace('-', '');
        sortQuery = `ORDER BY ${escapeKey(sortBy)} ${sortDir}`;
      }

      const query = `SELECT * FROM ${table}${whereQuery ? ' ' + whereQuery : ''}${sortQuery ? ' ' + sortQuery : ''};`;

      return request(query, params);
    },

    create: (data: DynamicObject) => {
      const keys = Object.keys(data).map((key) => escapeKey(key));
      const values = Object.values(data).map(() => '?');
      const params = Object.values(data);
      const query = `INSERT INTO ${table} (${keys.join(', ')}) VALUES (${values.join(', ')});`;

      return request(query, params);
    },

    update: (data: DynamicObject, options: SqlAdapterOptions) => {
      // apply set
      const dataQuery = Object.keys(data)
        .map((key) => `${escapeKey(key)}=?`)
        .join(', ');
      const params = [...Object.values(data), ...Object.values(options.where)];

      const query = `UPDATE ${table} SET ${dataQuery} ${assembleWhere(options.where)};`;

      return request(query, params);
    },

    delete: (options: SqlAdapterOptions) => {
      const params = Object.values(options.where);
      const query = `DELETE FROM ${table} ${assembleWhere(options.where)};`;

      return request(query, params);
    },
  };
};
