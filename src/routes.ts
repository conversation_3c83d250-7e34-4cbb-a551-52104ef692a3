import type { Express } from 'express';

import AccountController from '@api/controllers/account';
import AuthController from '@api/controllers/auth';
import GoalController from '@api/controllers/goal';
import HoldingController from '@api/controllers/holding';
import RateController from '@api/controllers/rate';
import RateMonthlyController from '@api/controllers/rate-monthly';
import ReportController from '@api/controllers/report';
import TaskController from '@api/controllers/task';
import UserController from '@api/controllers/user';

export default (router: Express) => {
  // accounts
  AccountController(router);

  // auth
  AuthController(router);

  // goals
  GoalController(router);

  // holdings
  HoldingController(router);

  // rates
  RateController(router);

  // rates monthly
  RateMonthlyController(router);

  // reports
  ReportController(router);

  // task
  TaskController(router);

  // user
  UserController(router);
};
