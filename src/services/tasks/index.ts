import { load } from 'cheerio';
import rp from 'request-promise';

const url = 'https://www.xe.com/currencytables/?from=USD';

type RateTd = {
  code: string;
  name: string;
  rate: number;
};

export const fetchCurrenciesTask = (): Promise<RateTd[]> =>
  rp(url).then((html) => {
    const $ = load(html);
    const rates: RateTd[] = [];

    $('table:nth-child(1) tr').each((i, tr) => {
      if (i > 0) {
        const tds: string[] = [];

        $(tr)
          .find('th,td')
          .each((_, td) => {
            tds.push($(td).text());
          });

        const [code, name, rate] = tds;

        rates.push({
          code,
          name,
          rate: Number(rate),
        });
      }
    });

    return rates;
  });
