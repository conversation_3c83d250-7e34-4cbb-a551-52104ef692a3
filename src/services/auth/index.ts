import { Request as ExpressRequest, Response as ExpressResponse, NextFunction } from 'express';
import jwt from 'jsonwebtoken';

import Env from '@api/services/env';
import { ERROR_AUTH } from '@api/lib/constants/error-codes';
import { RESPONSE_TYPE } from '@api/lib/constants/response-types';
import { Response } from '@api/lib/utils';
import { ErrorCode, User } from '@api/models';

const { secret, expire } = Env.jwt;

/**
 * Auth Service
 */
export default () => {
  const sign = (uid: string) =>
    jwt.sign({ uid }, secret, {
      expiresIn: expire,
    });

  const decode = (token: string) => jwt.verify(token, secret);

  const withAuthMiddleware = async (req: ExpressRequest, res: ExpressResponse, next: NextFunction) => {
    try {
      const token = req.headers.authorization?.split(' ')[1];

      if (!token) {
        throw ErrorCode({ code: ERROR_AUTH.TOKEN });
      }

      // @ts-expect-error - uid is there but not hard to type
      const { uid } = decode(token);

      const user = await User.findOne({ where: { id: uid, jwt: token } });

      if (user) {
        req.session.uid = user.id;
        res.locals.jwt = token;
        res.locals.user = user;
        res.locals.uid = user.id;

        next();
      } else {
        throw ErrorCode({ code: ERROR_AUTH.TOKEN });
      }
    } catch (err: unknown) {
      // Always use ERROR_AUTH.TOKEN for any auth failure
      const error = ErrorCode({ code: ERROR_AUTH.TOKEN });
      res.status(401).json(Response(RESPONSE_TYPE.ERROR, error));
    }
  };

  return {
    sign,
    decode,
    withAuthMiddleware,
  };
};
