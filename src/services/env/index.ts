type EnvType = {
  api: {
    host: string;
    namespace: string;
    port: number;
  };
  db: {
    host: string;
    name: string;
    pass: string;
    user: string;
  };
  jwt: {
    secret: string;
    expire: number;
  };
  redis: {
    host: string;
    pass: string;
    port: number;
    user: string;
  };
  session: {
    secret: string;
  };
};

const Env: EnvType = {
  api: {
    host: String(process.env.API_HOST),
    namespace: String(process.env.API_NAMESPACE),
    port: Number(process.env.API_PORT),
  },
  db: {
    host: String(process.env.DB_HOST),
    name: String(process.env.DB_NAME),
    pass: String(process.env.DB_PASS),
    user: String(process.env.DB_USER),
  },
  jwt: {
    secret: String(process.env.JWT_SECRET),
    expire: 86400 * Number(process.env.JWT_EXPIRE), // 86400 seconds in 1 day
  },
  redis: {
    host: String(process.env.REDIS_HOST),
    pass: String(process.env.REDIS_PASS),
    port: Number(process.env.REDIS_PORT),
    user: String(process.env.REDIS_USER),
  },
  session: {
    secret: String(process.env.SESSION_SECRET),
  },
};

export default Env;
