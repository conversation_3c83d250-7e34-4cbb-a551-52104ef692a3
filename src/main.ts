import compression from 'compression';
import { RedisStore } from 'connect-redis';
import Express from 'express';
import ExpressSession from 'express-session';
import logger from 'morgan';
import { createClient } from 'redis';

import { HEADER_CONTENT_TYPE } from '@api/lib/constants';
import routes from '@api/routes';
import { Env } from '@api/services';

// connect redis
const redisClient = createClient({
  url: `redis://${Env.redis.user}:${Env.redis.pass}@${Env.redis.host}:${Env.redis.port}`,
});
redisClient.connect().catch(console.error);

const store = new RedisStore({
  client: redisClient,
});

const expressApp = Express();
const limit = '10mb';

expressApp.use(logger('dev'));
expressApp.use(compression());
expressApp.use(Express.json({ limit, type: HEADER_CONTENT_TYPE }));
expressApp.use(Express.urlencoded({ extended: true, limit }));
expressApp.use(
  ExpressSession({
    resave: false,
    saveUninitialized: true,
    secret: Env.session.secret,
    store,
    cookie: {
      sameSite: 'none',
      secure: true,
    },
  }),
);

expressApp.use((_, res, next) => {
  // handle CORS
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Methods', 'GET,HEAD,OPTIONS,PATCH,POST,PUT,DELETE');
  res.header('Access-Control-Allow-Headers', 'Origin, X-Request-Id, Content-Type, Accept, authorization');

  next();
});

// initialize app with express
routes(expressApp);

/**
 * RUN SERVER
 */
const port = Env.api.port || 5001;

expressApp.listen(port, () => {
  console.log(`[ ready ] http://localhost:${port}`);
});
