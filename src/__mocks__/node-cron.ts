const scheduledJobs: { [key: string]: () => void } = {};

const cronMock = {
  schedule: jest.fn().mockImplementation((schedule, callback, options) => {
    const jobId = Math.random().toString(36).substring(7);
    scheduledJobs[jobId] = callback;

    return {
      start: jest.fn(),
      stop: jest.fn(),
      destroy: jest.fn(),
      getStatus: jest.fn().mockReturnValue('scheduled'),
    };
  }),
  validate: jest.fn().mockReturnValue(true),
  getTasks: jest.fn().mockReturnValue(scheduledJobs),
};

// Export both as default and named exports to support different import styles
module.exports = cronMock;
module.exports.default = cronMock;
module.exports.schedule = cronMock.schedule;
module.exports.validate = cronMock.validate;
module.exports.getTasks = cronMock.getTasks;
